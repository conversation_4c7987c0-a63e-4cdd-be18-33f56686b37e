//
//  LoginHelper.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON>ng ye on 2025/4/25.
//

import UIKit
import ATAuthSDK // 确保导入

class LoginHelper {

    static let shared = LoginHelper()
    private init() {}

    // 将 isHorizontal 移到这里，或者作为私有辅助函数
    private func isHorizontal(_ size: CGSize) -> Bool {
        return size.width > size.height
    }
    
    // 检查是否支持一键登录
    func checkOneClickLoginAvailability(completion: @escaping (Bool) -> Void) {
        // 调用阿里云SDK检查是否支持一键登录
        // PNSAuthTypeLoginToken = 2 (一键登录)
//        TXCommonHandler.sharedInstance().checkEnvAvailableWithAuthType(PNSAuthTypeLoginToken)) { (resultDic) in
        TXCommonHandler.sharedInstance().checkEnvAvailable(with: PNSAuthType.loginToken) { (resultDic) in
            guard let result = resultDic as? [AnyHashable: Any],
                  let code = result["resultCode"] as? String else {
                // 无法获取结果或结果不完整，视为不支持
                completion(false)
                return
            }
            
            print("一键登录环境检查结果: \(result)")
            
            // 600000表示成功
            let isSupported = code == "600000"
            completion(isSupported)
        }
    }

    // 封装的一键登录方法
    func presentOneClickLogin(from controller: UIViewController, completion: @escaping (Bool, String?) -> Void) {
        // --- 检查SDK是否初始化等前置条件 (如果需要) ---
        // ...

        print("准备调用阿里云一键登录...")
        
        // 增加防重复回调标识，确保 completion 只被触发一次
        var hasHandledResult = false

        // --- 创建和配置 TXCustomModel ---
        let model = configureCustomModel(for: controller.view.bounds.size)

        // --- 辅助函数：弹出原生登录页 ---
        func presentNativeLogin(fallbackMessage: String) {
            print("一键登录失败或不可用 (\(fallbackMessage))，静默切换到原生登录页...")
            DispatchQueue.main.async { // 确保在主线程操作 UI
                print("准备调用 cancelLoginVC (for fallback) - 不等待回调...")
                // 调用 cancelLoginVC 以防万一授权页部分显示，但不再等待其回调
                TXCommonHandler.sharedInstance().cancelLoginVC(animated: false)

                // **不再创建新的登录页面，而是直接通知调用者登录失败**
                print("一键登录失败，通知调用者")
                if !hasHandledResult {
                    hasHandledResult = true
                    completion(false, fallbackMessage)
                }
            }
        }


        // --- 调用 SDK 拉起授权页 ---
        TXCommonHandler.sharedInstance().getLoginToken(withTimeout: 8.0, controller: controller, model: model) { (resultDic) in
            guard let result = resultDic as? [AnyHashable: Any], let code = result["resultCode"] as? String else {
                // resultDic 为空或无法解析 resultCode，视为失败，触发 fallback
                presentNativeLogin(fallbackMessage: "SDK返回结果无效或无ResultCode")
                return
            }

            print("阿里云一键登录 complete 回调: \(result)")
            let message = result["msg"] as? String ?? "未知信息"

            switch code {
            case "600000": // PNSCodeSuccess: 成功获取 Token
                if let token = result["token"] as? String, !token.isEmpty {
                    print("获取 Token 成功: \(token)")
                    completion(true, token)
                } else {
                    print("获取 Token 成功，但 Token 为空，视为失败，触发 fallback")
                    presentNativeLogin(fallbackMessage: "获取Token成功但Token为空")
                }
            case "600001": // 授权页唤起成功
                print("授权页唤起成功 (中间状态，等待用户操作)")
                // 不需要在此处回调 completion 或 fallback

            // --- 以下为需要 Fallback 到原生登录页的 Case ---
            case "600002": // 授权页唤起失败
                print("授权页唤起失败，触发 fallback")
                presentNativeLogin(fallbackMessage: message)
            case "600004", "600015": // PNSCodeGetTokenTimeout, 获取Token超时
                print("获取Token超时，触发 fallback")
                presentNativeLogin(fallbackMessage: message)
            case "600005", "600011": // PNSCodeGetTokenFail, 获取Token失败
                print("获取Token失败，触发 fallback")
                presentNativeLogin(fallbackMessage: message)
            case "600013": // 运营商维护升级，该功能不可用
                 print("运营商维护升级，功能不可用，触发 fallback")
                 presentNativeLogin(fallbackMessage: message)
            case "600014": // 运营商维护升级，该功能已达最大调用次数
                 print("运营商维护升级，达最大调用次数，触发 fallback")
                 presentNativeLogin(fallbackMessage: message)

            // --- 以下为用户操作或 UI 事件，不 Fallback ---
            case "700000": // 点击授权页返回按钮
                print("用户点击返回按钮，取消登录")
                // 明确指出这是用户主动取消
                TXCommonHandler.sharedInstance().cancelLoginVC(animated: true)
                completion(false, "用户取消登录") // 用户主动取消，报告失败
            case "700001": // 点击切换其他登录方式 (UI 事件)
                print("用户点击切换其他登录方式 (UI 事件)")
                if !hasHandledResult {
                    hasHandledResult = true
                    // 关闭授权页，然后通知调用者用户想切换到其他登录方式
                    TXCommonHandler.sharedInstance().cancelLoginVC(animated: true) {
                        completion(false, "用户选择其他登录方式")
                    }
                } else {
                    TXCommonHandler.sharedInstance().cancelLoginVC(animated: true)
                }
            case "700002": // 点击登录按钮事件
                 let isChecked = result["isChecked"] as? Bool ?? false
                 print("用户点击登录按钮 (isChecked: \(isChecked))")
                 // 注意: privacyAlertIsNeedShow=true时，未勾选协议会自动显示提示弹窗
                 // SDK会自动处理未勾选的情况，无需额外逻辑
                 
            case "700003": // 点击 check box 事件
                 let isChecked = result["isChecked"] as? Bool ?? false
                 print("用户点击 check box (isChecked: \(isChecked))")
                 // 此处可以增加额外的勾选/取消勾选处理逻辑
                 // 如果需要在用户勾选时执行特定操作，可以在这里添加
                 
            case "700004": // 点击协议富文本文字
                 let privacyName = result["privacyName"] as? String ?? "未知协议"
                 let privacyUrl = result["privacyUrl"] as? String
                 print("用户点击协议: \(privacyName), URL: \(privacyUrl ?? "无")")
                 // 如果需要自定义协议点击行为，可以在这里拦截并处理
                 
            case "700006": // 弹出二次授权弹窗事件
                 print("正在弹出二次授权确认弹窗，等待用户操作...")
                 // 重要：这是正常流程，不要触发fallback
                 // 用户在弹窗中操作后会触发其他回调（如700007/600000等）
                 
            case "700007": // 点击二次弹窗按钮事件
                 print("用户点击了二次授权弹窗按钮")
                 // 用户点击了同意按钮，之后会自动处理登录
                 // 如果model.privacyAlertIsNeedAutoLogin = true，会自动触发登录流程
                 
            case "700020": // 授权页面释放事件 (LoginControllerDeallocVC)
                 print("授权页面释放事件 (700020)，可能是用户选择其他登录方式或关闭授权页")
                 if !hasHandledResult {
                     hasHandledResult = true
                     completion(false, "用户选择其他登录方式")
                 }

            // --- Default Case: 未知错误码，触发 Fallback ---
            default:
                // 检查是否是二次弹窗相关的返回码（700006-700010范围）
                if code.hasPrefix("7000") && Int(code) ?? 0 >= 700006 && Int(code) ?? 0 <= 700010 {
                    print("二次弹窗相关事件，返回码: \(code)，消息: \(message)，正常等待后续操作")
                    // 二次弹窗相关事件，不触发fallback
                } else {
                    print("一键登录失败: 未知返回码 \(code)，触发 fallback")
                    print("完整的返回数据: \(result)")
                    presentNativeLogin(fallbackMessage: "\(message) (Code: \(code))")
                }
            }
        }
    }

    // 查找当前最顶层的视图控制器
    private func getTopMostViewController() -> UIViewController? {
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let keyWindow = windowScene.windows.first(where: { $0.isKeyWindow }),
              var topController = keyWindow.rootViewController else {
            return nil
        }

        // 遍历查找 presentedViewController
        while let presentedController = topController.presentedViewController {
            topController = presentedController
        }

        // 处理 UINavigationController 或 UITabBarController 嵌套
        if let navController = topController as? UINavigationController {
            return navController.topViewController ?? topController // 返回导航栈顶的 VC
        } else if let tabController = topController as? UITabBarController {
            return tabController.selectedViewController ?? topController // 返回当前选中的 Tab VC
        }

        return topController
    }

    // 单独的配置 TXCustomModel 的方法，提高可读性
    private func configureCustomModel(for screenSize: CGSize) -> TXCustomModel {
        let model = TXCustomModel()

        // --- 导航栏 ---
        model.navColor = .white
        model.navTitle = NSAttributedString(string: "")
        if let backImage = UIImage(named: "nav_back") {
            model.navBackImage = backImage
        }
        // model.navIsHidden = true // 如果想完全隐藏导航栏

        // --- Logo ---
        if let logo = UIImage(named: "login_logo") {
            model.logoImage = logo
        }
        model.logoFrameBlock = { (screenSize: CGSize, superViewSize: CGSize, frame: CGRect) -> CGRect in
            var newFrame = frame
//             let topOffset: CGFloat = 20 // 根据需要调整
//             newFrame.origin.y = topOffset + (UIApplication.shared.windows.first?.safeAreaInsets.top ?? 0)
            newFrame.size = CGSize(width: 90, height: 90)
            newFrame.origin.y = 51
            // 保持 X 居中
//            newFrame.origin.x = (superViewSize.width - frame.width) / 2
            return newFrame
        }

        // --- Slogan ---
        model.sloganIsHidden = true

        // --- 手机号码 ---
        model.numberColor = .black
        model.numberFont = UIFont.systemFont(ofSize: 24.0)
        
        
        // --- 登录按钮 ---
        let loginBtnAttributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor.white,
            .font: UIFont.systemFont(ofSize: 18.0)
        ]
        // 注意：按钮文字不能为空，否则按钮可能不显示或行为异常
        model.loginBtnText = NSAttributedString(string: "", attributes: loginBtnAttributes)
        // --- 使用纯色作为按钮背景 ---
        // let buttonColor = UIColor(hex: "#FF5900") // 你的主色调
        // model.loginBtnBgColor = buttonColor
        // --- 或者继续使用背景图片 ---
         if let normalImage = UIImage(named: "login_text"), // 确保图片存在
            let disabledImage = UIImage(named: "login_text"), // 可以用同一张或不同的
            let highlightedImage = UIImage(named: "login_text") { // 可以用同一张或不同的
             model.loginBtnBgImgs = [normalImage, disabledImage, highlightedImage]
         } else {
             print("警告：未能加载登录按钮背景图片，将使用默认样式。")
             // 可以设置一个纯色背景作为备用
             // model.loginBtnBgColor = UIColor.orange
         }


        model.loginBtnFrameBlock = { screenSize, superViewSize, frame -> CGRect in
            // 定义期望的按钮尺寸和边距
            let desiredHeight: CGFloat = 48 // 根据你的图片 login_test
            let horizontalPadding: CGFloat = superViewSize.width - 343 // 左右边距，可以调整
            // 计算 X 坐标使其居中
            let originX = horizontalPadding / 2
            // 确保按钮在屏幕中间可见位置
            let originY = superViewSize.height / 2 - desiredHeight / 2 // 垂直居中
            print("设置登录按钮位置: x=\(originX), y=\(originY), width=343, height=\(desiredHeight), superViewSize=\(superViewSize)")
            return CGRect(x: originX, y: originY, width: 343, height: desiredHeight)
        }
        
        model.numberFrameBlock = { screenSize, superViewSize, frame -> CGRect in
            var newFrame = frame
            // 将手机号放在登录按钮上方一定距离
            newFrame.origin.y = superViewSize.height / 2 - 100 // 登录按钮上方100pt
            print("设置手机号位置: y=\(newFrame.origin.y), superViewSize=\(superViewSize)")
            return newFrame
        }
//
        // --- 隐私条款 ---
        model.privacyColors = [UIColor.lightGray, UIColor(hex: "#FB6C04")] // [默认文字颜色, 协议文字颜色]
        model.privacyAlignment = .center
        model.privacyFont = UIFont.systemFont(ofSize: 12.0)
        model.privacyOperatorPreText = "《"
        model.privacyOperatorSufText = "》"
        // **重要: 务必替换成你的有效协议 URL**
        model.privacyOne = ["《用户协议》", "https://gzyoushu.com/privacy/ysh-docuser.htm"]
        model.privacyTwo = ["《隐私政策》", "https://gzsxq.com/rule/sxq-privacy-policy.html"]
        // model.privacyThree = ["儿童隐私协议", "https://example.com/child-privacy"] // 如果有第三个

        // 设置运营商协议显示在最后位置
        model.privacyOperatorIndex = 2 // 0代表第一个位置，1代表第二个位置，2代表第三个位置(即最后)
        
        model.checkBoxWH = 17.0
        if let uncheckedImage = UIImage(named: "app_radio_Default"),
           let checkedImage = UIImage(named: "app_radio_select") {
            model.checkBoxImages = [uncheckedImage, checkedImage]
        } else {
            print("警告：未能加载隐私条款复选框图片。")
        }
        // 默认勾选
        model.checkBoxIsChecked = false
        model.checkBoxIsHidden = false // 确保复选框可见

        // 配置未勾选时的点击登录按钮弹窗提示
        // 启用二次弹窗提示
        model.privacyAlertIsNeedShow = true
        // 点击确认后自动执行登录，无需再次点击登录按钮
        model.privacyAlertIsNeedAutoLogin = true
        
        // 弹窗UI配置
        model.privacyAlertCornerRadiusArray = [10, 10, 10, 10] // 四个圆角
        model.privacyAlertBackgroundColor = .white
        model.privacyAlertAlpha = 1.0 // 确保不透明，避免显示问题
        model.privacyAlertTitleContent = "温馨提示"
        model.privacyAlertTitleFont = UIFont.systemFont(ofSize: 21, weight: .medium)
        model.privacyAlertTitleColor = .black
        model.privacyAlertTitleAlignment = .center
        
        // 弹窗内容配置
        model.privacyAlertContentFont = UIFont.systemFont(ofSize: 14)
        model.privacyAlertContentColors = [UIColor.darkGray, UIColor.orange]
        model.privacyAlertContentAlignment = .center
        model.privacyAlertPreText = "请阅读并同意"
        // 使用与主页面相同的协议配置
        model.privacyOperatorPreText = "《"
        model.privacyOperatorSufText = "》"
        
        // 弹窗按钮配置
        model.privacyAlertBtnContent = "同意并继续"
        model.privacyAlertBtnCornerRadius = 22
        // 定义橙色
        let orangeColor = UIColor(hex: "#FF8F1F")
        // 设置按钮文字颜色为白色
        model.privacyAlertButtonTextColors = [.white, .white]
        model.privacyAlertButtonFont = UIFont.systemFont(ofSize: 16, weight: .medium)
        
        
        // 关闭按钮配置
        model.privacyAlertCloseButtonIsNeedShow = true
        if let closeImage = UIImage(named: "requirement_unchecked") {
            model.privacyAlertCloseButtonImage = closeImage
        }
        
        // 为弹窗按钮生成纯橙色背景图片
        let buttonSize = CGSize(width: 200, height: 44) // 与之前保持一致或调整
        let cornerRadius: CGFloat = 22
        
        let normalButtonImage = UIImage.from(color: orangeColor, size: buttonSize, cornerRadius: cornerRadius)
        let highlightedButtonImage = UIImage.from(color: orangeColor.withAlphaComponent(0.8), size: buttonSize, cornerRadius: cornerRadius)
        model.privacyAlertBtnBackgroundImages = [normalButtonImage, highlightedButtonImage]

        // 弹窗背景蒙层配置
        model.privacyAlertMaskIsNeedShow = true
        model.privacyAlertMaskColor = .black
        model.privacyAlertMaskAlpha = 0.5
        model.tapPrivacyAlertMaskCloseAlert = true
        
        // 配置弹窗尺寸 - 尺寸要合适，确保内容能完整显示
        let alertWidth: CGFloat = min(UIScreen.main.bounds.width - 56, 300)
        let alertHeight: CGFloat = 240 // 可以根据内容调整
        model.privacyAlertFrameBlock = { (screenSize, superViewSize, frame) -> CGRect in
            let x = (superViewSize.width - alertWidth) / 2
            let y = (superViewSize.height - alertHeight) / 2
            return CGRect(x: x, y: y, width: alertWidth, height: alertHeight)
        }
        
        // 配置标题Frame (距离弹窗顶部12pt)
        model.privacyAlertTitleFrameBlock = { (screenSize, superViewSize, frame) -> CGRect in
            var titleFrame = frame
            titleFrame.origin.y = 12 // 顶部间距
            titleFrame.size.width = superViewSize.width // 宽度占满
            // 高度由SDK根据字体计算，这里可以不用设置，或者设置一个最小高度
            // titleFrame.size.height = max(frame.height, 25)
            return titleFrame
        }
        
        // 配置按钮Frame (靠下显示)
        let buttonHeight: CGFloat = 44
        let buttonBottomPadding: CGFloat = 40 // 按钮距离底部的距离
        model.privacyAlertButtonFrameBlock = { (screenSize, superViewSize, frame) -> CGRect in
            var buttonFrame = frame
            buttonFrame.size.height = buttonHeight
            buttonFrame.size.width = superViewSize.width * 0.7 // 按钮宽度为弹窗的70%
            buttonFrame.origin.x = (superViewSize.width - buttonFrame.width) / 2 // 水平居中
            buttonFrame.origin.y = superViewSize.height - buttonHeight - buttonBottomPadding // 靠下放置
            return buttonFrame
        }
        
        // 配置内容Frame (位于标题和按钮之间)
        model.privacyAlertPrivacyContentFrameBlock = { (screenSize, superViewSize, frame) -> CGRect in
            var contentFrame = frame
            let titleY_End = 12 + 50 // 假设标题高度大约25 (需要根据实际情况调整)
            let buttonY_Start = superViewSize.height - buttonHeight - buttonBottomPadding
            let contentTopPadding: CGFloat = 15 // 内容距离标题的距离
            let contentBottomPadding: CGFloat = 15 // 内容距离按钮的距离
            
            contentFrame.origin.x = 15 // 左右边距
            contentFrame.size.width = superViewSize.width - 30 // 宽度
            contentFrame.origin.y = CGFloat(titleY_End) + contentTopPadding
            contentFrame.size.height = buttonY_Start - contentFrame.origin.y - contentBottomPadding
            return contentFrame
        }
        
        // 处理未勾选协议时的动画效果
        // 协议文本的动画效果
        let privacyShakeAnimation = CAKeyframeAnimation(keyPath: "transform.translation.x")
        privacyShakeAnimation.values = [-5, 5, -5, 5, -3, 3, -2, 2, 0]
        privacyShakeAnimation.duration = 0.5
        model.privacyAnimation = privacyShakeAnimation
        
        // 复选框的动画效果
        let checkboxShakeAnimation = CAKeyframeAnimation(keyPath: "transform.translation.x")
        checkboxShakeAnimation.values = [-5, 5, -5, 5, -3, 3, -2, 2, 0]
        checkboxShakeAnimation.duration = 0.5
        model.checkboxAnimation = checkboxShakeAnimation
        
        // --- 切换其他方式按钮 ---
        let changeBtnAttributes: [NSAttributedString.Key: Any] = [
            .foregroundColor: UIColor.gray,
            .font: UIFont.systemFont(ofSize: 14.0)
        ]
        model.changeBtnTitle = NSAttributedString(string: "切换其他登录方式", attributes: changeBtnAttributes)
        model.changeBtnFrameBlock = { (screenSize, superViewSize, frame) -> CGRect in
//            let topOffset: CGFloat = 20 // 根据需要调整
//            let bottomOffset: CGFloat = 20 // 根据需要调整
//            let changeOffetY: CGFloat = changeBtnTopOffetY // 根据需要调整
            var newFrame = frame
//            newFrame.origin.y = superViewSize.height - frame.height - bottomOffset
//            // 保持 X 居中
//            newFrame.origin.x = (superViewSize.width - frame.width) / 2
            newFrame.origin.y = 644
            return newFrame
        }

        // --- 状态栏 ---
        model.preferredStatusBarStyle = .default // 深色内容，因为背景是白色

        // --- 横竖屏支持 ---
        model.supportedInterfaceOrientations = .portrait // 仅竖屏

        // --- 添加自定义控件 ---
        // 可以保持这部分逻辑不变，或者根据需要调整
        let titleImageViewTag = 1001 // 给自定义标题图片设置一个唯一的tag
        weak var titleImageViewRef: UIImageView?

        model.customViewBlock = { superCustomView in
            // 尝试获取或创建标题图片视图，并保存引用
            if let existing = superCustomView.viewWithTag(titleImageViewTag) as? UIImageView {
                titleImageViewRef = existing
            } else if let titleImage = UIImage(named: "login_title_ct") {
                let titleImageView = UIImageView(image: titleImage)
                titleImageView.tag = titleImageViewTag
                superCustomView.addSubview(titleImageView)
                titleImageViewRef = titleImageView
                print("已添加自定义标题图片 login_title_ct")
            } else {
                print("警告：未能加载自定义标题图片 login_title_ct")
            }
        }

        model.customViewLayoutBlock = { screenSize, contentViewFrame, navFrame, titleBarFrame, logoFrame, sloganFrame, numberFrame, loginFrame, changeBtnFrame, privacyFrame in
            guard let customTitleImageView = titleImageViewRef else { return }
            let imageWidth: CGFloat = 216
            let imageHeight: CGFloat = 21
            let originX = (contentViewFrame.width - imageWidth) / 2
            // 将标题图片放在Logo下方 28pt
            let originY = logoFrame.maxY + 28
            // 如果计算的位置超出可视区域，则放到屏幕1/4处作为兜底
            let finalOriginY: CGFloat
            if originY < 0 || originY > contentViewFrame.height {
                finalOriginY = contentViewFrame.height * 0.25
                print("调整自定义标题图片位置(原位置不可见)，frame.y 调整为: \(finalOriginY)")
            } else {
                finalOriginY = originY
            }
            customTitleImageView.frame = CGRect(x: originX, y: finalOriginY, width: imageWidth, height: imageHeight)
            print("设置自定义标题图片位置，frame: \(customTitleImageView.frame)")
        }
        // ... (之前的 titleLeftImageView, titleRightImageView 逻辑) ...
        // 注意：如果添加了自定义控件，需要在 customViewBlock 和 customViewLayoutBlock 中处理
        model.privacyNavBackImage = UIImage(named: "nav_back")!
        
        model.privacyFrameBlock = { (screenSize: CGSize, superViewSize: CGSize, frame: CGRect) -> CGRect in
            var newFrame = frame
            // 将隐私协议放在登录按钮下方一定距离
            newFrame.origin.y = superViewSize.height / 2 + 60 // 登录按钮下方60pt
            print("设置隐私协议位置: y=\(newFrame.origin.y), superViewSize=\(superViewSize)")
            return newFrame
        }

        return model
    }
}

// MARK: - UIImage扩展
extension UIImage {
    /// 创建纯色图像，支持圆角设置
    static func from(color: UIColor, size: CGSize = CGSize(width: 1, height: 1), cornerRadius: CGFloat = 0) -> UIImage {
        let rect = CGRect(origin: .zero, size: size)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        color.setFill()
        
        if cornerRadius > 0 {
            let path = UIBezierPath(roundedRect: rect, cornerRadius: cornerRadius)
            path.fill()
        } else {
            UIRectFill(rect)
        }
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image ?? UIImage()
    }
    
    /// 创建带边框的纯色图像
    static func createButtonImage(size: CGSize, fillColor: UIColor, borderColor: UIColor, borderWidth: CGFloat, cornerRadius: CGFloat) -> UIImage {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        guard let context = UIGraphicsGetCurrentContext() else {
            UIGraphicsEndImageContext()
            return UIImage()
        }
        
        let rect = CGRect(origin: .zero, size: size)
        let path = UIBezierPath(roundedRect: rect.insetBy(dx: borderWidth / 2, dy: borderWidth / 2), cornerRadius: cornerRadius)
        
        // 填充背景色
        fillColor.setFill()
        path.fill()
        
        // 描边
        borderColor.setStroke()
        path.lineWidth = borderWidth
        path.stroke()
        
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image ?? UIImage()
    }
}
