import UIKit
import MapKit

class MapSearchResultViewController: UIViewController, UITableViewDataSource, UITableViewDelegate, MKMapViewDelegate, UISearchBarDelegate {
    private let mapView = MKMapView()
    private let tableView = UITableView()
    private var searchResults: [MKMapItem] = []
    private let centerCoordinate: CLLocationCoordinate2D
    private var emptyLabel: UILabel?
    private let searchBar = UISearchBar()
    private var currentKeywords: [String] = ["商圈", "购物中心", "商场", "便利店", "餐厅"]
    
    init(centerCoordinate: CLLocationCoordinate2D) {
        self.centerCoordinate = centerCoordinate
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "附近商圈/店铺"
        view.backgroundColor = .systemBackground
        setupSearchBar()
        setupMapView()
        setupTableView()
        performMultiKeywordSearch(keywords: currentKeywords)
    }
    
    private func setupSearchBar() {
        searchBar.placeholder = "输入关键词（如餐厅/便利店/商场）"
        searchBar.delegate = self
        searchBar.returnKeyType = .search
        searchBar.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(searchBar)
        NSLayoutConstraint.activate([
            searchBar.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            searchBar.leftAnchor.constraint(equalTo: view.leftAnchor),
            searchBar.rightAnchor.constraint(equalTo: view.rightAnchor),
            searchBar.heightAnchor.constraint(equalToConstant: 56)
        ])
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(mapView)
        let region = MKCoordinateRegion(center: centerCoordinate, latitudinalMeters: 3000, longitudinalMeters: 3000)
        mapView.setRegion(region, animated: false)
        let annotation = MKPointAnnotation()
        annotation.coordinate = centerCoordinate
        annotation.title = "锚点"
        mapView.addAnnotation(annotation)
        NSLayoutConstraint.activate([
            mapView.topAnchor.constraint(equalTo: searchBar.bottomAnchor),
            mapView.leftAnchor.constraint(equalTo: view.leftAnchor),
            mapView.rightAnchor.constraint(equalTo: view.rightAnchor),
            mapView.heightAnchor.constraint(equalToConstant: 220)
        ])
    }
    
    private func setupTableView() {
        tableView.dataSource = self
        tableView.delegate = self
        tableView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(tableView)
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: mapView.bottomAnchor),
            tableView.leftAnchor.constraint(equalTo: view.leftAnchor),
            tableView.rightAnchor.constraint(equalTo: view.rightAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }
    
    // 多关键词合并搜索
    private func performMultiKeywordSearch(keywords: [String]) {
        var allItems: [MKMapItem] = []
        let group = DispatchGroup()
        let region = mapView.region
        for keyword in keywords {
            group.enter()
            let request = MKLocalSearch.Request()
            request.naturalLanguageQuery = keyword
            request.region = region
            let search = MKLocalSearch(request: request)
            search.start { response, error in
                if let items = response?.mapItems {
                    allItems.append(contentsOf: items)
                }
                group.leave()
            }
        }
        group.notify(queue: .main) {
            // 合并去重（按坐标和主名）
            let unique = Dictionary(grouping: allItems, by: { "\($0.placemark.coordinate.latitude),\($0.placemark.coordinate.longitude),\(self.filteredName($0.name))" })
            self.searchResults = unique.values.compactMap { $0.first }
            self.tableView.reloadData()
            // 清除旧标注
            let toRemove = self.mapView.annotations.filter { !($0 is MKUserLocation) && ($0.title ?? "") != "锚点" }
            self.mapView.removeAnnotations(toRemove)
            // 在地图上添加标注
            for item in self.searchResults {
                let annotation = MKPointAnnotation()
                annotation.coordinate = item.placemark.coordinate
                annotation.title = self.filteredName(item.name)
                self.mapView.addAnnotation(annotation)
            }
            if self.searchResults.isEmpty {
                self.showEmptyLabel()
            } else {
                self.hideEmptyLabel()
            }
        }
    }
    
    private func showEmptyLabel() {
        if emptyLabel == nil {
            let label = UILabel()
            label.text = "附近没有找到商圈/店铺"
            label.textAlignment = .center
            label.textColor = .secondaryLabel
            label.font = .systemFont(ofSize: 18, weight: .medium)
            label.translatesAutoresizingMaskIntoConstraints = false
            view.addSubview(label)
            NSLayoutConstraint.activate([
                label.centerXAnchor.constraint(equalTo: view.centerXAnchor),
                label.centerYAnchor.constraint(equalTo: tableView.centerYAnchor)
            ])
            emptyLabel = label
        }
        emptyLabel?.isHidden = false
    }
    
    private func hideEmptyLabel() {
        emptyLabel?.isHidden = true
    }
    
    // 过滤掉楼层、门牌号等详细信息，只显示主名
    private func filteredName(_ name: String?) -> String {
        guard let name = name else { return "" }
        let pattern = "[一-龥A-Za-z0-9]+(大厦|广场|中心|商场|写字楼|公寓|小区|市场|超市|酒店|宾馆|医院|学校|园区|街|路|巷|村|镇|区|县|市|省)"
        if let match = name.range(of: pattern, options: .regularExpression) {
            return String(name[match])
        }
        return name
    }
    
    // MARK: - UISearchBarDelegate
    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        searchBar.resignFirstResponder()
        let text = searchBar.text?.trimmingCharacters(in: .whitespacesAndNewlines) ?? ""
        if text.isEmpty {
            currentKeywords = ["商圈", "购物中心", "商场", "便利店", "餐厅"]
        } else {
            currentKeywords = text.components(separatedBy: [" ", ",", ";"]).filter { !$0.isEmpty }
        }
        performMultiKeywordSearch(keywords: currentKeywords)
    }
    
    // MARK: - UITableViewDataSource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return searchResults.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell") ?? UITableViewCell(style: .subtitle, reuseIdentifier: "cell")
        let item = searchResults[indexPath.row]
        cell.textLabel?.text = filteredName(item.name)
        cell.detailTextLabel?.text = item.placemark.title
        cell.accessibilityLabel = filteredName(item.name)
        return cell
    }
    
    // MARK: - UITableViewDelegate
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let item = searchResults[indexPath.row]
        // 高亮地图上的对应标注
        for annotation in mapView.annotations {
            if let point = annotation as? MKPointAnnotation, point.coordinate.latitude == item.placemark.coordinate.latitude, point.coordinate.longitude == item.placemark.coordinate.longitude {
                mapView.selectAnnotation(point, animated: true)
                mapView.setCenter(point.coordinate, animated: true)
                break
            }
        }
    }
} 
