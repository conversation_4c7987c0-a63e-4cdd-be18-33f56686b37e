import UIKit
import MapKit

class MapSearchTestViewController: UIViewController, MKMapViewDelegate {
    private let mapView = MKMapView()
    private var selectedCoordinate: CLLocationCoordinate2D?
    private let nextButton = UIButton(type: .system)
    
    override func viewDidLoad() {
        super.viewDidLoad()
        title = "选择锚点"
        view.backgroundColor = .systemBackground
        setupMapView()
        setupNextButton()
    }
    
    private func setupMapView() {
        mapView.delegate = self
        mapView.showsUserLocation = true
        mapView.userTrackingMode = .follow
        mapView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(mapView)
        NSLayoutConstraint.activate([
            mapView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            mapView.leftAnchor.constraint(equalTo: view.leftAnchor),
            mapView.rightAnchor.constraint(equalTo: view.rightAnchor),
            mapView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -80)
        ])
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(handleLongPress(_:)))
        mapView.addGestureRecognizer(longPress)
    }
    
    private func setupNextButton() {
        nextButton.setTitle("下一步", for: .normal)
        nextButton.titleLabel?.font = .boldSystemFont(ofSize: 18)
        nextButton.backgroundColor = UIColor.systemBlue
        nextButton.setTitleColor(.white, for: .normal)
        nextButton.layer.cornerRadius = 22
        nextButton.translatesAutoresizingMaskIntoConstraints = false
        nextButton.addTarget(self, action: #selector(nextButtonTapped), for: .touchUpInside)
        view.addSubview(nextButton)
        NSLayoutConstraint.activate([
            nextButton.bottomAnchor.constraint(equalTo: view.safeAreaLayoutGuide.bottomAnchor, constant: -16),
            nextButton.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            nextButton.widthAnchor.constraint(equalToConstant: 180),
            nextButton.heightAnchor.constraint(equalToConstant: 44)
        ])
        nextButton.isEnabled = false
        nextButton.alpha = 0.5
    }
    
    @objc private func handleLongPress(_ gesture: UILongPressGestureRecognizer) {
        if gesture.state == .began {
            let point = gesture.location(in: mapView)
            let coordinate = mapView.convert(point, toCoordinateFrom: mapView)
            selectedCoordinate = coordinate
            mapView.removeAnnotations(mapView.annotations)
            let annotation = MKPointAnnotation()
            annotation.coordinate = coordinate
            annotation.title = "锚点"
            mapView.addAnnotation(annotation)
            nextButton.isEnabled = true
            nextButton.alpha = 1.0
        }
    }
    
    @objc private func nextButtonTapped() {
        guard let coordinate = selectedCoordinate else { return }
        let resultVC = MapSearchResultViewController(centerCoordinate: coordinate)
        navigationController?.pushViewController(resultVC, animated: true)
    }
} 