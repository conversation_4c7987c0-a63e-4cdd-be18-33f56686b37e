//
//  MapTest.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON><PERSON> ye on 2025/5/28.
//

import UIKit
import CoreLocation
import MapKit
import SnapKit

class MapTestViewController: BaseViewController, CLLocationManagerDelegate, UITableViewDataSource, UITableViewDelegate {
    private let locationManager = CLLocationManager()
    private let statusLabel = UILabel()
    private let coordinateLabel = UILabel()
    private let addressLabel = UILabel()
    private let startButton = UIButton(type: .system)
    private let stopButton = UIButton(type: .system)
    private let singleButton = UIButton(type: .system)
    private let tableView = UITableView()
    private var fuzzyAddresses: [(name: String, distance: Double)] = []
    private var currentLocation: CLLocation?
    private var lastPOISearchTime: Date?
    private var lastMainBuildingName: String?
    private var allPOIItems: [MKMapItem] = []
    // 测试开关：是否全部展示POI结果（不过滤重复）
    private let showAllPOIResults: Bool = false
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white
        navTitle = "地图测试"
        setupUI()
        setupLocationManager()
        updateStatusLabel()
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        updateStatusLabel()

        testPOICoordinateSystems()
    }
    
    private func setupUI() {
        // 状态Label
        statusLabel.numberOfLines = 0
        statusLabel.textAlignment = .center
        statusLabel.font = .systemFont(ofSize: 15)
        statusLabel.textColor = .darkGray
        statusLabel.text = "定位权限状态：--"
        
        // 经纬度Label
        coordinateLabel.numberOfLines = 0
        coordinateLabel.textAlignment = .center
        coordinateLabel.font = .systemFont(ofSize: 15)
        coordinateLabel.textColor = .darkGray
        coordinateLabel.text = "经度：--\n纬度：--"
        
        // 地址Label
        addressLabel.numberOfLines = 0
        addressLabel.textAlignment = .center
        addressLabel.font = .systemFont(ofSize: 15)
        addressLabel.textColor = .darkGray
        addressLabel.text = "详细地址：--"
        
        // 按钮
        startButton.setTitle("长期获取位置", for: .normal)
        startButton.addTarget(self, action: #selector(startUpdatingLocation), for: .touchUpInside)
        stopButton.setTitle("暂停", for: .normal)
        stopButton.addTarget(self, action: #selector(stopUpdatingLocation), for: .touchUpInside)
        singleButton.setTitle("获取一次位置", for: .normal)
        singleButton.addTarget(self, action: #selector(requestSingleLocation), for: .touchUpInside)
        
        // 横向按钮堆叠
        let buttonStack = UIStackView(arrangedSubviews: [startButton, stopButton, singleButton])
        buttonStack.axis = .horizontal
        buttonStack.spacing = 16
        buttonStack.distribution = .fillEqually
        
        // 垂直堆叠Label和按钮
        let stack = UIStackView(arrangedSubviews: [statusLabel, coordinateLabel, addressLabel, buttonStack])
        stack.axis = .vertical
        stack.spacing = 16
        stack.alignment = .fill
        
        contentView.addSubview(stack)
        stack.snp.makeConstraints { make in
            make.top.equalTo(contentView).offset(40)
            make.left.right.equalTo(contentView).inset(20)
            make.height.lessThanOrEqualTo(contentView).multipliedBy(0.5)
        }
        
        // 下方TableView
        tableView.dataSource = self
        tableView.delegate = self
        tableView.tableFooterView = UIView()
        contentView.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(stack.snp.bottom).offset(16)
            make.left.right.bottom.equalTo(contentView)
        }
    }
    
    private func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyHundredMeters
        locationManager.distanceFilter = 50
    }
    
    private func updateStatusLabel() {
        let status: String
        switch locationManager.authorizationStatus {
        case .notDetermined: status = "未决定"
        case .restricted: status = "受限"
        case .denied: status = "拒绝"
        case .authorizedAlways: status = "始终允许"
        case .authorizedWhenInUse: status = "使用期间允许"
        @unknown default: status = "未知"
        }
        statusLabel.text = "定位权限状态：\(status)"
    }
    
    // MARK: - 按钮事件
    @objc private func startUpdatingLocation() {
        locationManager.startUpdatingLocation()
    }
    @objc private func stopUpdatingLocation() {
        locationManager.stopUpdatingLocation()
    }
    @objc private func requestSingleLocation() {
        if #available(iOS 9.0, *) {
            locationManager.requestLocation()
        } else {
            locationManager.startUpdatingLocation()
        }
        // 不再在这里直接查POI，等didUpdateLocations回调
    }
    
    // MARK: - CLLocationManagerDelegate
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        updateStatusLabel()
    }
    
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        guard let location = locations.last else { return }
        currentLocation = location
        let latitude = location.coordinate.latitude
        let longitude = location.coordinate.longitude
        coordinateLabel.text = String(format: "经度：%.6f\n纬度：%.6f", longitude, latitude)
        reverseGeocodeLocation(location)
        print("测试打印-locations: \(locations)")
        searchNearbyPOIsForAllLocations(locations)
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        addressLabel.text = "详细地址：获取失败"
        fuzzyAddresses = []
        tableView.reloadData()
    }
    
    // 智能提取大厦/地标主名
    private func extractMainBuildingName(from placemark: CLPlacemark) -> String? {
        let candidates = [
            placemark.name,
            placemark.thoroughfare,
            placemark.subThoroughfare,
            placemark.subLocality,
            placemark.locality
        ].compactMap { $0 }
        let pattern = "[一-龥A-Za-z0-9]+(大厦|广场|中心|小区|写字楼|公寓|市场|超市|酒店|宾馆|医院|学校|园区)"
        for text in candidates {
            if let range = text.range(of: pattern, options: .regularExpression) {
                return String(text[range])
            }
        }
        return nil
    }
    
    private func reverseGeocodeLocation(_ location: CLLocation) {
        let geocoder = CLGeocoder()
        // 新增：打印各种坐标转换结果
        let wgsCoord = location.coordinate
        let gcjCoord = CoordinateTransform.wgs84ToGcj02(wgsCoord)
        let bd09Coord = CoordinateTransform.gcj02ToBd09(gcjCoord)
        let gcjFromBd09 = CoordinateTransform.bd09ToGcj02(bd09Coord)
        let bd09FromGcj = CoordinateTransform.gcj02ToBd09(gcjCoord)
        let owgs4 = CoordinateTransform.gcj02ToWgs84(wgsCoord)
        print("【坐标转换】WGS-84原始: \(wgsCoord)")
        print("【坐标转换】WGS-84 -> WGS-84: \(owgs4)")
        print("【坐标转换】WGS-84 -> GCJ-02(高德): \(gcjCoord)")
        print("【坐标转换】WGS-84 -> BD-09(百度): \(bd09Coord)")
        print("【坐标转换】BD-09(百度) -> GCJ-02(高德): \(gcjFromBd09)")
        print("【坐标转换】GCJ-02(高德) -> BD-09(百度): \(bd09FromGcj)")
        geocoder.reverseGeocodeLocation(location) { [weak self] (placemarks, error) in
            print("测试打印-locations: \(location)")
            guard let self = self else { return }
            DispatchQueue.main.async {
                if let error = error {
                    self.addressLabel.text = "详细地址：获取失败"
                    return
                }
                guard let placemarks = placemarks, !placemarks.isEmpty else {
                    self.addressLabel.text = "详细地址：获取失败"
                    return
                }
                // 完整多字段拼接
                let placemark = placemarks[0]
                var detail = ""
                if let name = placemark.name { detail += name }
                if let subThoroughfare = placemark.subThoroughfare, !subThoroughfare.isEmpty { detail += subThoroughfare }
                if let thoroughfare = placemark.thoroughfare, !thoroughfare.isEmpty { detail += "，\(thoroughfare)" }
                if let subLocality = placemark.subLocality, !subLocality.isEmpty { detail += "，\(subLocality)" }
                if let locality = placemark.locality, !locality.isEmpty { detail += "，\(locality)" }
                if let administrativeArea = placemark.administrativeArea, !administrativeArea.isEmpty { detail += "，\(administrativeArea)" }
                if let country = placemark.country, !country.isEmpty { detail += "，\(country)" }
                self.addressLabel.text = "详细地址：\(detail)"
                print("【定位回调】详细地址: \(detail)")
                // 智能提取主名
                if let mainName = self.extractMainBuildingName(from: placemark) {
                    self.lastMainBuildingName = mainName
                }
            }
        }
    }
    
    // 坐标系转换工具
    struct CoordinateTransform {
        static let a = 6378245.0
        static let ee = 0.00669342162296594323

        static func isInChina(_ coordinate: CLLocationCoordinate2D) -> Bool {
            let lon = coordinate.longitude
            let lat = coordinate.latitude
            return (lon > 72.004 && lon < 137.8347) && (lat > 0.8293 && lat < 55.8271)
        }

        static func wgs84ToGcj02(_ coordinate: CLLocationCoordinate2D, decimalPlaces: Int? = nil) -> CLLocationCoordinate2D {
            let lat = coordinate.latitude
            let lon = coordinate.longitude
            if !isInChina(coordinate) { return coordinate }
            var dLat = transformLat(x: lon - 105.0, y: lat - 35.0)
            var dLon = transformLon(x: lon - 105.0, y: lat - 35.0)
            let radLat = lat / 180.0 * Double.pi
            var magic = sin(radLat)
            magic = 1 - ee * magic * magic
            let sqrtMagic = sqrt(magic)
            dLat = (dLat * 180.0) / ((a * (1 - ee)) / (magic * sqrtMagic) * Double.pi)
            dLon = (dLon * 180.0) / (a / sqrtMagic * cos(radLat) * Double.pi)
            var result = CLLocationCoordinate2D(latitude: lat + dLat, longitude: lon + dLon)
            if let places = decimalPlaces {
                let factor = pow(10.0, Double(places))
                result = CLLocationCoordinate2D(
                    latitude: (result.latitude * factor).rounded() / factor,
                    longitude: (result.longitude * factor).rounded() / factor
                )
            }
            return result
        }

        static func gcj02ToWgs84(_ coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
            let lat = coordinate.latitude
            let lon = coordinate.longitude
            if !isInChina(coordinate) { return coordinate }
            let gcj = wgs84ToGcj02(coordinate)
            let dLat = gcj.latitude - lat
            let dLon = gcj.longitude - lon
            return CLLocationCoordinate2D(latitude: lat - dLat, longitude: lon - dLon)
        }

        // BD-09 -> GCJ-02
        static func bd09ToGcj02(_ coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
            let x = coordinate.longitude - 0.0065
            let y = coordinate.latitude - 0.006
            let z = sqrt(x * x + y * y) - 0.00002 * sin(y * Double.pi)
            let theta = atan2(y, x) - 0.000003 * cos(x * Double.pi)
            let gcjLon = z * cos(theta)
            let gcjLat = z * sin(theta)
            return CLLocationCoordinate2D(latitude: gcjLat, longitude: gcjLon)
        }

        // GCJ-02 -> BD-09
        static func gcj02ToBd09(_ coordinate: CLLocationCoordinate2D) -> CLLocationCoordinate2D {
            let x = coordinate.longitude
            let y = coordinate.latitude
            let z = sqrt(x * x + y * y) + 0.00002 * sin(y * Double.pi)
            let theta = atan2(y, x) + 0.000003 * cos(x * Double.pi)
            let bdLon = z * cos(theta) + 0.0065
            let bdLat = z * sin(theta) + 0.006
            return CLLocationCoordinate2D(latitude: bdLat, longitude: bdLon)
        }

        private static func transformLat(x: Double, y: Double) -> Double {
            var ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * sqrt(abs(x))
            ret += (20.0 * sin(6.0 * x * Double.pi) + 20.0 * sin(2.0 * x * Double.pi)) * 2.0 / 3.0
            ret += (20.0 * sin(y * Double.pi) + 40.0 * sin(y / 3.0 * Double.pi)) * 2.0 / 3.0
            ret += (160.0 * sin(y / 12.0 * Double.pi) + 320 * sin(y * Double.pi / 30.0)) * 2.0 / 3.0
            return ret
        }

        private static func transformLon(x: Double, y: Double) -> Double {
            var ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * sqrt(abs(x))
            ret += (20.0 * sin(6.0 * x * Double.pi) + 20.0 * sin(2.0 * x * Double.pi)) * 2.0 / 3.0
            ret += (20.0 * sin(x * Double.pi) + 40.0 * sin(x / 3.0 * Double.pi)) * 2.0 / 3.0
            ret += (150.0 * sin(x / 12.0 * Double.pi) + 300.0 * sin(x / 30.0 * Double.pi)) * 2.0 / 3.0
            return ret
        }
    }
    
    /// 多点POI搜索：遍历所有定位点，查找100米范围内的店面或写字楼
    /// - Parameters:
    ///   - locations: 输入的定位点数组
    ///   - coordinateType: 输入坐标类型（"WGS84"、"GCJ02"、"BD09"），默认"GCJ02"
    private func searchNearbyPOIsForAllLocations(_ locations: [CLLocation], coordinateType: String = "GCJ02") {
        // 1. 系统POI分类
        let categories: [MKPointOfInterestCategory] = [
            .store,          // 商场、大卖场
            .hospital,       // 医院
            .publicTransport // 地铁/公交站
        ]

        // 2. 关键词补充
        let keywords = [
            "写字楼", "办公楼", "大厦", "广场", "商场", "购物中心", "地铁站", "店"
        ]
        var allItems: [MKMapItem] = []
        let group = DispatchGroup()
        var regionCenters: [CLLocation] = []
        for location in locations {
            let inputCoord = location.coordinate
            var wgs84Coord: CLLocationCoordinate2D = inputCoord
            switch coordinateType.uppercased() {
            case "GCJ02":
                wgs84Coord = CoordinateTransform.wgs84ToGcj02(inputCoord, decimalPlaces: 6)
                print("[POI] 输入GCJ-02: \(inputCoord) -> WGS-84: \(wgs84Coord)")
            case "BD09":
                let gcjCoord = CoordinateTransform.bd09ToGcj02(inputCoord)
                wgs84Coord = CoordinateTransform.gcj02ToWgs84(gcjCoord)
                print("[POI] 输入BD-09: \(inputCoord) -> GCJ-02: \(gcjCoord) -> WGS-84: \(wgs84Coord)")
            case "WGS84":
                print("[POI] 输入WGS-84: \(inputCoord)")
                wgs84Coord = inputCoord
            default:
                print("[POI] 未知坐标类型，默认按WGS-84处理: \(inputCoord)")
                wgs84Coord = inputCoord
            }
            regionCenters.append(CLLocation(latitude: wgs84Coord.latitude, longitude: wgs84Coord.longitude))
            // 分类检索
            let baseRequest = MKLocalSearch.Request()
            baseRequest.region = MKCoordinateRegion(center: wgs84Coord, latitudinalMeters: 300, longitudinalMeters: 300)
            baseRequest.pointOfInterestFilter = MKPointOfInterestFilter(including: categories)
            group.enter()
            MKLocalSearch(request: baseRequest).start { response, error in
                if let items = response?.mapItems {
                    allItems.append(contentsOf: items)
                }
                group.leave()
            }
            // 关键词检索
            for keyword in keywords {
                let req = MKLocalSearch.Request()
                req.naturalLanguageQuery = keyword
                req.region = baseRequest.region
                group.enter()
                MKLocalSearch(request: req).start { response, error in
                    if let items = response?.mapItems {
                        allItems.append(contentsOf: items)
                    }
                    group.leave()
                }
            }
        }
        group.notify(queue: .main) {
            var items: [MKMapItem]
            if self.showAllPOIResults {
                items = allItems
            } else {
                // 合并去重（按名称+≈50m坐标网格）
                let unique = Dictionary(grouping: allItems) { item -> String in
                    let lat = String(format: "%.4f", item.placemark.coordinate.latitude)
                    let lon = String(format: "%.4f", item.placemark.coordinate.longitude)
                    let name = (item.name ?? "_").lowercased()
                    return "\(name)@\(lat),\(lon)"
                }.compactMapValues { $0.first }
                items = unique.values.map { $0 }
            }
            // 距离排序（以最近region.center为参考点）
            func nearestRegionCenter(to poi: MKMapItem) -> CLLocation? {
                guard let poiLoc = poi.placemark.location else { return nil }
                return regionCenters.min(by: { $0.distance(from: poiLoc) < $1.distance(from: poiLoc) })
            }
            items.sort { a, b in
                guard let la = a.placemark.location, let lb = b.placemark.location else { return false }
                let refA = nearestRegionCenter(to: a) ?? la
                let refB = nearestRegionCenter(to: b) ?? lb
                return la.distance(from: refA) < lb.distance(from: refB)
            }
            // 填充fuzzyAddresses用于tableView
            self.fuzzyAddresses = items.compactMap { item in
                if let n = item.name, let loc = item.placemark.location {
                    let ref = nearestRegionCenter(to: item) ?? loc
                    let dist = loc.distance(from: ref)
                    return (n, dist)
                }
                return nil
            }
            self.allPOIItems = items // 新增：保存完整POI信息用于点击cell时打印
            self.tableView.reloadData()
            print("POI多坐标系搜索完成！")
        }
    }
    
    // MARK: - UITableViewDataSource
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return fuzzyAddresses.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "cell") ?? UITableViewCell(style: .value1, reuseIdentifier: "cell")
        let (name, distance) = fuzzyAddresses[indexPath.row]
        cell.textLabel?.text = name
        cell.textLabel?.numberOfLines = 0
        // 距离显示优化：仅当距离大于0且有效时展示，否则隐藏
//        if distance > 0.1 {
            if distance < 1000 {
                cell.detailTextLabel?.text = String(format: "%dm", Int(distance))
            } else {
                cell.detailTextLabel?.text = String(format: "%.1fkm", distance/1000)
            }
//        } else {
//            cell.detailTextLabel?.text = nil
//        }
        return cell
    }
    
    // MARK: - UITableViewDelegate
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        // 打印POI详情
        guard indexPath.row < allPOIItems.count else { return }
        let item = allPOIItems[indexPath.row]
        let name = item.name ?? "?"
        let coord = item.placemark.coordinate
        let address = item.placemark.title ?? ""
        let phone = item.phoneNumber ?? ""
        print("[POI详情] 名称: \(name)\n经纬度: \(coord)\n地址: \(address)\n电话: \(phone)")
    }
    
    // MARK: - 坐标系POI搜索测试
    func testPOICoordinateSystems() {
        let wgsCoord = CLLocationCoordinate2D(latitude: 23.144748092336457, longitude: 113.33657839180965)
//        let wgs84Coord = CoordinateTransform.wgs84ToGcj02(wgsCoord)
        let wgs84Coord = CLLocationCoordinate2D(latitude: 23.141875092336457, longitude: 113.34201839180965)
//        let gcjCoord = CoordinateTransform.wgs84ToGcj02(wgsCoord)
        let gcjCoord = CLLocationCoordinate2D(latitude: 23.142122092336457, longitude: 113.34230839180965)
//        let bd09Coord = CoordinateTransform.gcj02ToBd09(gcjCoord)
        let bd09Coord = CLLocationCoordinate2D(latitude: 23.142176405634338, longitude: 113.34202881008784)
        let keyword = "光大银行大厦"
        let meters: CLLocationDistance = 2000
        let group = DispatchGroup()
        let originWGS = CLLocation(latitude: wgs84Coord.latitude, longitude: wgs84Coord.longitude)
        // 1. WGS-84
        group.enter()
        let req1 = MKLocalSearch.Request()
        req1.naturalLanguageQuery = keyword
        req1.region = MKCoordinateRegion(center: wgs84Coord, latitudinalMeters: meters, longitudinalMeters: meters)
        let search1 = MKLocalSearch(request: req1)
        search1.start { response, error in
            if let item = response?.mapItems.first, let loc = item.placemark.location {
                let dist = loc.distance(from: originWGS)
                let poiCoord = item.placemark.coordinate
                print("[1] WGS-84中心点: \(wgs84Coord) -> POI: \(item.name ?? "?") 距离: \(dist)m, POI坐标: \(poiCoord)")
            } else {
                print("[1] WGS-84中心点: 无POI结果")
            }
            group.leave()
        }
        // 2. GCJ-02
        group.enter()
        let req2 = MKLocalSearch.Request()
        req2.naturalLanguageQuery = keyword
        req2.region = MKCoordinateRegion(center: gcjCoord, latitudinalMeters: meters, longitudinalMeters: meters)
        let search2 = MKLocalSearch(request: req2)
        search2.start { response, error in
            if let item = response?.mapItems.first, let loc = item.placemark.location {
                let dist = loc.distance(from: originWGS)
                let poiCoord = item.placemark.coordinate
                print("[2] GCJ-02中心点: \(gcjCoord) -> POI: \(item.name ?? "?") 距离: \(dist)m, POI坐标: \(poiCoord)")
            } else {
                print("[2] GCJ-02中心点: 无POI结果")
            }
            group.leave()
        }
        // 3. BD-09
        group.enter()
        let req3 = MKLocalSearch.Request()
        req3.naturalLanguageQuery = keyword
        req3.region = MKCoordinateRegion(center: bd09Coord, latitudinalMeters: meters, longitudinalMeters: meters)
        let search3 = MKLocalSearch(request: req3)
        search3.start { response, error in
            if let item = response?.mapItems.first, let loc = item.placemark.location {
                let dist = loc.distance(from: originWGS)
                let poiCoord = item.placemark.coordinate
                print("[3] BD-09中心点: \(bd09Coord) -> POI: \(item.name ?? "?") 距离: \(dist)m, POI坐标: \(poiCoord)")
            } else {
                print("[3] BD-09中心点: 无POI结果")
            }
            group.leave()
        }
        // 等待所有完成
        group.notify(queue: .main) {
            print("POI坐标系测试完成！")
        }
    }
}
