//
//  SettingViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/3/19.
//

import UIKit
import SnapKit
import Foundation

class SettingViewController: BaseViewController, UITableViewDelegate, UITableViewDataSource {
    
    // 表格视图
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = UIColor(hex: "#F5F5F5")
        tableView.separatorStyle = .none
        tableView.register(SettingCell.self, forCellReuseIdentifier: "SettingCell")
        tableView.contentInsetAdjustmentBehavior = .never
        // 设置表格视图的内边距
        tableView.contentInset = UIEdgeInsets(top: 12, left: 0, bottom: 12, right: 0)
        return tableView
    }()
    
    // 设置数据
    private var sections: [SettingSection] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        // 设置标题
        navTitle = "设置"
        
        // 设置视图背景色
        view.backgroundColor = .white
        
        // 设置表格视图
        setupTableView()
        
        // 设置数据
        setupData()
    }
    
    private func setupTableView() {
        // 添加表格视图到内容视图
        contentView.addSubview(tableView)
        
        // 设置表格视图约束
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupData() {
        // 第一组：账号和通知设置
        let section1 = SettingSection(items: [
            SettingItem(icon: "setting_account", title: "账号设置", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToAccountSettings()
            }),
            SettingItem(icon: "setting_notification", title: "通知设置", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToNotificationSettings()
            }),
            SettingItem(icon: "setting_permission", title: "应用权限", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToPermissionSettings()
            })
        ], showFooter: true)
        
        // 第二组：应用权限、规则中心、用户协议、隐私政策
        let section2 = SettingSection(items: [
            SettingItem(icon: "setting_rules", title: "规则中心", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToRulesCenter()
            }),
            SettingItem(icon: "setting_agreement", title: "用户协议", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToUserAgreement()
            }),
            SettingItem(icon: "setting_privacy", title: "隐私政策", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToPrivacyPolicy()
            }),
            SettingItem(icon: "setting_certification", title: "资质证照", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToCertification()
            })
        ], showFooter: true)
        
        // 第三组：资质证明、反馈与帮助、关于我们
        let section3 = SettingSection(items: [
            SettingItem(icon: "setting_feedback", title: "反馈与帮助", rightText: nil, showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToFeedback()
            }),
            SettingItem(icon: "setting_about", title: "关于我们", rightText: "v1.0.0", showArrow: true, isHighlighted: false, action: { [weak self] in
                self?.navigateToAbout()
            })
        ], showFooter: true)
        
        // 第四组：切换账号、退出登录
        let section4 = SettingSection(items: [
            SettingItem(icon: "setting_switch", title: "切换账号", rightText: nil, showArrow: false, isHighlighted: false, action: { [weak self] in
                self?.switchAccount()
            }),
            SettingItem(icon: "setting_logout", title: "退出登录", rightText: nil, showArrow: false, isHighlighted: true, action: { [weak self] in
                self?.logout()
            })
        ], showFooter: false)
        
        // 设置数据
        sections = [section1, section2, section3, section4]
        
        // 刷新表格视图
        tableView.reloadData()
    }
    
    // MARK: - UITableViewDataSource
    
    func numberOfSections(in tableView: UITableView) -> Int {
        return sections.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return sections[section].items.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "SettingCell", for: indexPath) as! SettingCell
        let item = sections[indexPath.section].items[indexPath.row]
        cell.configure(with: item)
        return cell
    }
    
    // MARK: - UITableViewDelegate
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 56 // 设置单元格高度为56
    }
    
    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return section == 0 ? 0 : 12 // 第一个分组没有头部，其他分组头部高度为12
    }
    
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return sections[section].showFooter ? 1 : 0.01 // 如果显示尾部，高度为1，否则为0.01（不能为0）
    }
    
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        if section == 0 {
            return nil // 第一个分组没有头部
        }
        
        let headerView = UIView()
        headerView.backgroundColor = UIColor(hex: "#F5F5F5") // 设置背景色为浅灰色
        return headerView
    }
    
    func tableView(_ tableView: UITableView, viewForFooterInSection section: Int) -> UIView? {
        if !sections[section].showFooter {
            return nil // 如果不显示尾部，返回nil
        }
        
        let footerView = UIView()
        footerView.backgroundColor = UIColor(hex: "#F5F5F5") // 设置背景色为浅灰色
        return footerView
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        // 执行点击操作
        let item = sections[indexPath.section].items[indexPath.row]
        item.action?()
    }
    
    // MARK: - 导航方法
    
    private func navigateToAccountSettings() {
        print("导航到账号设置")
        // 已登录，跳转到账号设置页面
        let accountSettingsViewController = AccountSettingViewController()
        navigationController?.pushViewController(accountSettingsViewController, animated: true)
    }
    
    // 修改：检查用户登录状态的辅助方法
    private func isUserLoggedIn() -> Bool {
        return AuthManager.shared.isLoggedIn
    }
    
    private func navigateToNotificationSettings() {
        print("导航到通知设置")
        let notificationSettingsViewController = NotificationSettingsViewController()
        navigationController?.pushViewController(notificationSettingsViewController, animated: true)
        // 实现导航到通知设置页面的逻辑
    }
    
    private func navigateToPermissionSettings() {
        print("导航到应用权限")
        // 实现导航到应用权限页面的逻辑
        let webView = WebViewController(url: URL(string: "https://gzsxq.com/rule/sxq-privacy-policy.html")!,title: " ")
        self.navigationController?.pushViewController(webView, animated: true)
    }
    
    private func navigateToRulesCenter() {
        print("导航到规则中心")
        let webView = WebViewController(url: URL(string: "https://gzsxq.com/rule/RuleCenter.html")!,title: " ")
        self.navigationController?.pushViewController(webView, animated: true)
    }
    
    private func navigateToUserAgreement() {
        print("导航到用户协议")
        // 实现导航到用户协议页面的逻辑
        let webView = WebViewController(url: URL(string: "https://gzyoushu.com/privacy/ysh-docuser.htm")!,title: " ")
        self.navigationController?.pushViewController(webView, animated: true)
    }
    
    private func navigateToPrivacyPolicy() {
        print("导航到隐私政策")
        // 实现导航到隐私政策页面的逻辑
        let webView = WebViewController(url: URL(string: "https://gzsxq.com/rule/sxq-privacy-policy.html")!,title: " ")
        self.navigationController?.pushViewController(webView, animated: true)
    }
    
    private func navigateToCertification() {
        print("导航到资质证照")
        // 实现导航到资质证明页面的逻辑
        let webView = WebViewController(url: URL(string: "https://gzsxq.com/rule/certification.html")!,title: "资质证照")
        self.navigationController?.pushViewController(webView, animated: true)
    }
    
    private func navigateToFeedback() {
        print("导航到反馈与帮助")
        //pages/user/feedBack/feedBack
        let webVC = WebViewController(path: "pages/user/feedBack/feedBack", title: "反馈与帮助")
        navigationController?.pushViewController(webVC, animated: true)
    }
    
    private func navigateToAbout() {
        print("导航到关于我们")
        // 实现导航到关于我们页面的逻辑
        
        self.navigationController?.pushViewController(AboutUsViewController(), animated: true)
    }
    
    // MARK: - 自定义弹窗
    private func presentCustomAlert(title: String,
                                    message: String? = nil,
                                    leftTitle: String = "取消",
                                    rightTitle: String = "确定",
                                    confirmHandler: @escaping () -> Void) {
        let alert = CommonAlertView(title: title,
                                    message: message ?? "",
                                    leftButtonTitle: leftTitle,
                                    rightButtonTitle: rightTitle)
        alert.onLeftButtonTap = {
            alert.dismiss()
        }
        alert.onRightButtonTap = {
            alert.dismiss()
            confirmHandler()
        }
        alert.show()
    }
    
    private func switchAccount() {
        print("切换账号")
        // 使用自定义弹窗进行确认
        presentCustomAlert(title: "是否切换账号？") { [weak self] in
            self?.performCoreLogout { [weak self] in
                guard let self = self else { return }
                print("切换账号：核心退出完成，执行返回首页并准备弹出登录页")
                // 1. 返回首页并切换 Tab
                self.navigateBackAndSwitchToHome()
                // 2. 稍作延迟后弹出登录页
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                    let loginVC = LoginViewController2_0()
                    loginVC.modalPresentationStyle = .fullScreen
                    var presentingVC: UIViewController? = self.findTabBarController()
                    if presentingVC == nil {
                        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                           let keyWindow = windowScene.windows.first(where: { $0.isKeyWindow }) {
                            presentingVC = keyWindow.rootViewController
                        }
                    }
                    if let vc = presentingVC {
                        vc.present(loginVC, animated: true, completion: nil)
                    } else {
                        self.present(loginVC, animated: true, completion: nil)
                    }
                }
            }
        }
    }
    
    private func logout() {
        print("退出登录")
        // 使用自定义弹窗确认退出（只弹一次）
        presentCustomAlert(title: "确认退出登录？") { [weak self] in
            self?.performCoreLogout { [weak self] in
                self?.navigateBackAndSwitchToHome()
            }
        }
    }

    // 新增：处理退出登录按钮的完整流程（核心退出 + 后续UI操作）
    private func handleLogoutAction() {
        performCoreLogout { [weak self] in
            guard let self = self else { return }
            print("退出登录：核心退出完成，显示成功提示并导航")

            // 显示操作成功的提示
            let successAlert = UIAlertController(
                title: "退出成功",
                message: "您已成功退出登录",
                preferredStyle: .alert
            )
            let okAction = UIAlertAction(title: "确定", style: .default) { [weak self] _ in
                self?.navigateBackAndSwitchToHome()
            }
            successAlert.addAction(okAction)

            // 在主线程显示成功提示
            DispatchQueue.main.async {
                self.present(successAlert, animated: true, completion: nil)
            }
        }
    }

    // 新增：核心退出登录逻辑（API + 本地清理）
    private func performCoreLogout(completion: @escaping () -> Void) {
        print("开始执行核心退出登录…")
        // 可选：显示加载指示器 showLoading() 如果有实现
        APIManager.shared.logout { [weak self] result in
            guard let self = self else { return }
            // 可选：隐藏加载指示器 hideLoading()
            switch result {
            case .success(let response):
                if response.status == 200 {
                    // 清理本地 Token
                    AuthManager.shared.logout()
                    print("AuthManager.shared.logout() 已调用")
                    // 清理 WKWebView 登录缓存
                    WebViewController.clearLoginCache {
                        print("WebViewController.clearLoginCache 完成")
                        completion() // 继续后续流程（返回首页 / 弹登录页）
                    }
                } else {
                    // 接口返回非 200，Toast 提示错误，不执行后续流程
                    DispatchQueue.main.async {
                        self.showToast(response.displayMessage)
                    }
                }
            case .failure(let error):
                // 网络或服务器错误，同样 Toast 提示
                DispatchQueue.main.async {
                    self.showToast(error.errorMessage)
                }
            }
        }
    }

    // 新增：退出登录成功后的导航和 Tab 切换逻辑
    private func navigateBackAndSwitchToHome() {
        print("执行退出登录后的导航：返回并切换到首页")
        // 第一步：如果当前页面是被push的，先返回到"我的"页面
        if let navigationController = self.navigationController {
            // 打印当前导航栈
            print("当前导航栈: \\(navigationController.viewControllers.map { type(of: $0) })")

            // 先返回到"我的"页面（父页面），不使用动画
            navigationController.popViewController(animated: false)
            print("已返回到'我的'页面")

            // 尝试获取我的页面控制器并调用切换方法
            if let meViewController = navigationController.topViewController as? MeViewController {
                print("找到MeViewController，直接调用其switchToHomeTab方法")
                meViewController.switchToHomeTab()
            } else {
                print("返回后的顶层控制器不是MeViewController，使用备用方法切换")

                // 第二步：使用备用方法尝试切换到首页
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    // 查找TabBarController
                    if let tabBarController = self.findTabBarController() {
                        print("找到TabBarController，准备切换到首页")

                        // 判断是否为自定义TabBarController
                        if let customTabBar = tabBarController as? CustomTabBarController {
                            print("通过CustomTabBarController专有方法切换")
                            customTabBar.selectTab(at: 0)
                        } else {
                            // 标准TabBarController
                            tabBarController.selectedIndex = 0
                            print("通过selectedIndex=0切换到首页")
                        }

                        // 方法3：通过代理方法触发选择
                        if let delegate = tabBarController.delegate {
                            if tabBarController.viewControllers?.count ?? 0 > 0,
                               let firstVC = tabBarController.viewControllers?[0] {
                                print("方法3：通过TabBarController代理方法触发选择")
                                delegate.tabBarController?(tabBarController, didSelect: firstVC)
                            }
                        }

                        // 方法4：使用NotificationCenter广播通知
                        print("方法4：发送通知以更新TabBar")
                        NotificationCenter.default.post(name: NSNotification.Name("TabBarShouldSelectHomeTab"), object: nil, userInfo: ["index": 0])
                    } else {
                        print("未找到TabBarController，无法切换到首页")
                    }
                }
            }
        } else {
            print("未找到导航控制器，可能不是以导航方式呈现的设置页面")

            // 如果不是通过导航控制器呈现的，尝试直接使用TabBarController
            if let tabBarController = self.findTabBarController() {
                print("找到TabBarController，切换到首页")
                tabBarController.selectedIndex = 0
            }
        }
    }

    // 辅助方法：查找TabBarController
    private func findTabBarController() -> UITabBarController? {
        // 从导航控制器中查找TabBarController
        if let tabBarController = self.navigationController?.tabBarController {
            return tabBarController
        }
        
        // 从窗口中查找TabBarController
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let keyWindow = windowScene.windows.first(where: { $0.isKeyWindow }),
              let rootVC = keyWindow.rootViewController else {
            return nil
        }
        
        // 如果根视图是TabBarController
        if let tabBarController = rootVC as? UITabBarController {
            return tabBarController
        }
        
        // 如果根视图导航控制器
        if let navController = rootVC as? UINavigationController,
           let tabBarController = navController.viewControllers.first as? UITabBarController {
            return tabBarController
        }
        
        return nil
    }
}

// 设置单元格
class SettingCell: UITableViewCell {
    
    // 图标视图
    private let iconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 16)
        label.textColor = UIColor(hex: "#333333")
        return label
    }()
    
    // 右侧文本标签
    private let rightTextLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 14)
        label.textColor = UIColor(hex: "#999999")
        label.textAlignment = .right
        return label
    }()
    
    // 箭头图标
    private let arrowImageView: UIImageView = {
        let imageView = UIImageView(image: UIImage(named: "setting_arrow"))
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // 分隔线
    private let separatorLine: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#E7E7E7")
        return view
    }()
    
    // 内容容器视图
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        // 设置背景色
        backgroundColor = UIColor.clear
        selectionStyle = .none
        
        // 添加容器视图
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        // 添加图标视图
        containerView.addSubview(iconImageView)
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        // 添加标题标签
        containerView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(12)
            make.centerY.equalToSuperview()
        }
        
        // 添加箭头图标
        containerView.addSubview(arrowImageView)
        arrowImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(16)
            make.height.equalTo(16)
        }
        
        // 添加右侧文本标签
        containerView.addSubview(rightTextLabel)
        rightTextLabel.snp.makeConstraints { make in
            make.right.equalTo(arrowImageView.snp.left).offset(-8)
            make.centerY.equalToSuperview()
        }
        
        // 添加分隔线
        containerView.addSubview(separatorLine)
        separatorLine.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(52) // 与标题左对齐
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }
    
    func configure(with item: SettingItem) {
        // 设置图标
        iconImageView.image = UIImage(named: item.icon)
        
        // 设置标题
        titleLabel.text = item.title
        
        // 设置标题颜色
        if item.isHighlighted {
            titleLabel.textColor = UIColor(hex: "#FF3B30") // 红色
        } else {
            titleLabel.textColor = UIColor(hex: "#333333") // 默认颜色
        }
        
        // 设置右侧文本
        rightTextLabel.text = item.rightText
        rightTextLabel.isHidden = item.rightText == nil
        
        // 设置箭头
        arrowImageView.isHidden = !item.showArrow
    }
}
