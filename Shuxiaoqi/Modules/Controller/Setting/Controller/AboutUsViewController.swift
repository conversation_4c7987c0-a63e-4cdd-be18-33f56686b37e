//
//  AboutUsViewController.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/4/29.
//
//  关于我们

import UIKit
import SnapKit

class AboutUsViewController: BaseViewController {

    // 滚动视图
    private let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = true
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    // 滚动内容容器
    private let scrollContentView: UIView = {
        let view = UIView()
        return view
    }()
    
    // App Logo
    private let logoImageView: UIImageView = {
        let imageView = UIImageView()
//        imageView.backgroundColor = UIColor(hex: "#FF7F00") // 使用与截图匹配的橙色
        imageView.contentMode = .scaleAspectFit
        imageView.image = UIImage(named: "login_logo") // 需要添加 app_logo 图片资源
        return imageView
    }()

    // 版本信息容器
    private let versionInfoContainer: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        view.layer.masksToBounds = true
        return view
    }()

    // 当前版本
    private let currentVersionLabel = createLeftLabel(text: "当前版本")
    private let currentVersionValueLabel = createRightLabel(text: "v1.0.0")

    // 最新版本
    private let latestVersionLabel = createLeftLabel(text: "最新版本")
    private let latestVersionValueLabel = createRightLabel(text: "v1.0.0可更新", color: UIColor(hex: "#FF7F00"))

    // 发布日期
    private let releaseDateLabel = createLeftLabel(text: "发布日期")
    private let releaseDateValueLabel = createRightLabel(text: "-")

    // 分隔线及行的引用，便于后续隐藏
    private var releaseDateRow: UIView?
    private var separator2: UIView?
    private var separator3: UIView?
    private var releaseDateRowHeightConstraint: Constraint?

    // 更新按钮
    private let updateButton: UIButton = {
        let button = UIButton(type: .system)
        
        // 创建带下载图标的标题
        let downloadIcon = UIImage(named: "icon_download") ?? UIImage(systemName: "arrow.down.circle.fill")
        var configuration = UIButton.Configuration.filled()
        configuration.baseBackgroundColor = UIColor(hex: "#FF8D1A") // 橙色背景
        configuration.baseForegroundColor = .white // 白色前景
        configuration.title = "更新到最新版本"
//        configuration.titleLabel?.font =
        configuration.image = downloadIcon
        configuration.imagePadding = 8 // 图标与文本的间距
        configuration.imagePlacement = .leading // 图标放在前面
        configuration.cornerStyle = .medium // 圆角
        button.titleLabel?.font = .boldSystemFont(ofSize: 16)
        button.configuration = configuration
        return button
    }()

    // 记录更新按钮的顶部约束，便于动态调整
    private var updateButtonTopConstraint: Constraint?

    // 相关链接标题
    private let relatedLinksLabel: UILabel = {
        let label = UILabel()
        label.text = "相关链接"
        label.font = .boldSystemFont(ofSize: 16)
        label.textColor = UIColor(hex: "#707070")
        label.backgroundColor = .white // 确保背景色与卡片一致
        return label
    }()

    // 相关链接容器 (使用 StackView 简化)
    private let linksStackView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        view.layer.cornerRadius = 12
        
        // 添加阴影效果
        view.layer.shadowColor = UIColor.black.withAlphaComponent(0.1).cgColor
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        view.layer.shadowOpacity = 1
        view.layer.masksToBounds = false
        
        return view
    }()
    
    // 内部使用的真正 StackView
    private let linksContentStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fill
        stackView.backgroundColor = .white
        stackView.layer.cornerRadius = 12
        stackView.clipsToBounds = true
        return stackView
    }()

    override func viewDidLoad() {
        super.viewDidLoad()
        navTitle = "关于"
        view.backgroundColor = .white
        contentView.backgroundColor = .white
        setupScrollView()
        setupUI()
        setupActions()
        
        // 加载当前App版本信息
        loadAppVersion()
    }
    
    private func setupScrollView() {
        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(scrollView)
            // 根据屏幕高度决定是否启用滚动
            if UIScreen.main.bounds.height < 896.0 {
                // 内容高度自适应
                make.height.greaterThanOrEqualTo(scrollView)
            } else {
                // 大屏幕设备固定高度等于scrollView高度
                make.height.equalTo(scrollView)
            }
        }
    }

    private func setupUI() {
        // Logo
        scrollContentView.addSubview(logoImageView)
        logoImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(31)
            make.centerX.equalToSuperview()
            make.size.equalTo(100) // 根据截图调整大小
        }

        // 版本信息
        scrollContentView.addSubview(versionInfoContainer)
        versionInfoContainer.snp.makeConstraints { make in
            make.top.equalTo(logoImageView.snp.bottom).offset(79)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
        }

        // 版本信息行 - 改为直接添加三行，而不使用UIStackView
        // 当前版本行
        let currentVersionRow = createVersionRow(left: currentVersionLabel, right: currentVersionValueLabel)
        versionInfoContainer.addSubview(currentVersionRow)
        currentVersionRow.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(54) // 设置更合适的行高
        }
        
        // 第一条分隔线
        let separator1 = createSeparatorLine()
        versionInfoContainer.addSubview(separator1)
        separator1.snp.makeConstraints { make in
            make.top.equalTo(currentVersionRow.snp.bottom)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(0.5)
        }
        
        // 最新版本行
        let latestVersionRow = createVersionRow(left: latestVersionLabel, right: latestVersionValueLabel)
        versionInfoContainer.addSubview(latestVersionRow)
        latestVersionRow.snp.makeConstraints { make in
            make.top.equalTo(separator1.snp.bottom)
            make.left.right.equalToSuperview()
            make.height.equalTo(54)
        }
        
        // 第二条分隔线
        self.separator2 = createSeparatorLine()
        guard let separator2 = self.separator2 else { return }
        versionInfoContainer.addSubview(separator2)
        separator2.snp.makeConstraints { make in
            make.top.equalTo(latestVersionRow.snp.bottom)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(0.5)
        }
        
        // 发布日期行
        self.releaseDateRow = createVersionRow(left: releaseDateLabel, right: releaseDateValueLabel)
        guard let releaseDateRow = self.releaseDateRow else { return }
        versionInfoContainer.addSubview(releaseDateRow)
        releaseDateRow.snp.makeConstraints { make in
            make.top.equalTo(separator2.snp.bottom)
            make.left.right.equalToSuperview()
            self.releaseDateRowHeightConstraint = make.height.equalTo(54).constraint
            make.bottom.equalToSuperview() // 确保此行是容器的底部
        }
        
        // 第三条分隔线
        self.separator3 = createSeparatorLine()
        guard let separator3 = self.separator3 else { return }
        versionInfoContainer.addSubview(separator3)
        separator3.snp.makeConstraints { make in
            make.top.equalTo(releaseDateRow.snp.bottom)
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.height.equalTo(0.5)
        }

        // 更新按钮
        scrollContentView.addSubview(updateButton)
        updateButton.snp.makeConstraints { make in
            self.updateButtonTopConstraint = make.top.equalTo(versionInfoContainer.snp.bottom).offset(25).constraint
            make.left.equalToSuperview().offset(28)
            make.right.equalToSuperview().offset(-28)
            make.height.equalTo(50) // 根据截图调整高度
        }

        // 相关链接卡片容器
        scrollContentView.addSubview(linksStackView)
        linksStackView.snp.makeConstraints { make in
            make.top.equalTo(updateButton.snp.bottom).offset(26) // 直接连接到更新按钮
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            // 允许底部更灵活，避免顶部移动后被强行拉伸
            make.bottom.lessThanOrEqualToSuperview().offset(-20)
        }
        
        // 添加内容 StackView 到容器
        linksStackView.addSubview(linksContentStackView)
        linksContentStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 添加相关链接标题作为卡片的第一项
        addHeaderToLinks()
        
        // 添加一条粗分隔线，分隔标题和内容
//        let headerSeparator = UIView()
//        headerSeparator.backgroundColor = UIColor(hex: "#F0F0F0")
//        linksContentStackView.addArrangedSubview(headerSeparator)
//        headerSeparator.snp.makeConstraints { make in
//            make.height.equalTo(1) // 稍微粗一点的分隔线
//        }

        // 添加链接行
        addLinkRow(iconName: "icon_agreement", fallbackSystemName: "doc.text", title: "用户协议", action: #selector(userAgreementTapped))
        addSeparatorLine(to: linksContentStackView)
        addLinkRow(iconName: "icon_privacy", fallbackSystemName: "lock.shield", title: "隐私政策", action: #selector(privacyPolicyTapped))
        addSeparatorLine(to: linksContentStackView)
        addLinkRow(iconName: "icon_support", fallbackSystemName: "person.circle", title: "联系客服", action: #selector(contactSupportTapped))
    }

    // 添加相关链接标题到卡片中
    private func addHeaderToLinks() {
        let headerContainer = UIView()
        headerContainer.backgroundColor = .white
        
        headerContainer.addSubview(relatedLinksLabel)
        relatedLinksLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }
        
        linksContentStackView.addArrangedSubview(headerContainer)
        headerContainer.snp.makeConstraints { make in
            make.height.equalTo(50) // 适当的标题高度
        }
    }

    // 创建版本信息行
    private func createVersionRow(left: UILabel, right: UILabel) -> UIView {
        let rowView = UIView()
        rowView.backgroundColor = .white
        rowView.addSubview(left)
        rowView.addSubview(right)

        left.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }
        right.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        return rowView
    }

    // 创建分隔线
    private func createSeparatorLine() -> UIView {
        let line = UIView()
        line.backgroundColor = UIColor(hex: "#F0F0F0")
        line.snp.makeConstraints { make in
            make.height.equalTo(0.5)
        }
        return line
    }
    
    // 添加分隔线到 StackView (带左右边距)
    private func addSeparatorLine(to stackView: UIStackView) {
        let container = UIView()
        let line = UIView()
        line.backgroundColor = UIColor(hex: "#F0F0F0")
        container.addSubview(line)
        line.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.top.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
        stackView.addArrangedSubview(container)
        container.snp.makeConstraints { make in
            make.height.equalTo(0.5)
        }
    }

    // 添加链接行到 StackView
    private func addLinkRow(iconName: String, fallbackSystemName: String, title: String, action: Selector) {
        let rowView = UIView()
        rowView.backgroundColor = .white
        rowView.isUserInteractionEnabled = true
        rowView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: action))

        let iconImageView = UIImageView()
        // 尝试使用自定义图标，如果不存在则使用系统图标
        if let customIcon = UIImage(named: iconName) {
            iconImageView.image = customIcon
        } else {
            iconImageView.image = UIImage(systemName: fallbackSystemName)?.withRenderingMode(.alwaysTemplate)
            iconImageView.tintColor = UIColor.darkGray.withAlphaComponent(0.7) // 使用深灰色，略微透明
        }
        iconImageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .boldSystemFont(ofSize: 16)
        titleLabel.textColor = UIColor(hex: "#707070")

        let arrowImageView = UIImageView()
        // 使用系统的右箭头图标
        arrowImageView.image = UIImage(systemName: "chevron.right")?.withRenderingMode(.alwaysTemplate)
        arrowImageView.tintColor = UIColor.lightGray
        arrowImageView.contentMode = .scaleAspectFit

        rowView.addSubview(iconImageView)
        rowView.addSubview(titleLabel)
        rowView.addSubview(arrowImageView)

        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(30) // 调整图标大小
        }
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(14) // 增加间距
            make.centerY.equalToSuperview()
        }
        arrowImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(14) // 调整箭头大小
        }
        
        linksContentStackView.addArrangedSubview(rowView)
        rowView.snp.makeConstraints { make in
            make.height.equalTo(60) // 增加行高与截图匹配
        }
    }

    // MARK: - App Version
    private func loadAppVersion() {
        // 获取当前版本号
        guard let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String else {
            return
        }
        currentVersionValueLabel.text = "v\(appVersion)"
        fetchLatestVersion(currentVersion: appVersion)
    }

    /// 调用接口获取最新版本信息
    private func fetchLatestVersion(currentVersion: String) {
        print("[AboutUs] 开始获取版本信息: 当前版本 \(currentVersion)")
        APIManager.shared.getAppVersion(appVersion: currentVersion) { [weak self] result in
            guard let self = self else { return }
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    print("[AboutUs] 版本接口返回成功: \(response.data.count) 条数据")
                    guard response.isSuccess, let latestData = response.data.max(by: { self.isVersion($0.appVersion, lessThan: $1.appVersion) }) else {
                        print("[AboutUs] 没有获取到有效的版本数据，当前版本为最新")
                        self.updateUIForLatestVersion(isLatest: true)
                        return
                    }
                    let latestVersion = latestData.appVersion
                    let hasUpdate = self.isVersion(currentVersion, lessThan: latestVersion)
                    let releaseDate = latestData.updateTime ?? ""
                    print("[AboutUs] 最新版本: \(latestVersion), 需要更新: \(hasUpdate), 发布日期: \(releaseDate)")
                    self.updateUI(latestVersion: latestVersion, hasUpdate: hasUpdate, releaseDate: releaseDate)
                case .failure(let error):
                    // 接口失败时，仅显示当前版本
                    print("[AboutUs] 版本接口请求失败: \(error)")
                    self.updateUIForLatestVersion(isLatest: true)
                }
            }
        }
    }

    private func updateUI(latestVersion: String, hasUpdate: Bool, releaseDate: String?) {
        print("[AboutUs] 更新UI: 最新版本=\(latestVersion), 需要更新=\(hasUpdate), 发布日期=\(releaseDate ?? "无")")
        if hasUpdate {
            // 可更新
            latestVersionValueLabel.text = "v\(latestVersion) 可更新"
            latestVersionValueLabel.textColor = UIColor(hex: "#FF9500")

            updateButton.isEnabled = true
            var config = updateButton.configuration
            config?.title = "更新到最新版本"
            config?.baseBackgroundColor = UIColor(hex: "#FF8D1A")
            updateButton.configuration = config

            // 有更新时始终显示发布日期行
            print("[AboutUs] 有更新，显示发布日期行")
            if let date = releaseDate, !date.isEmpty {
                print("[AboutUs] 显示发布日期: \(date)")
                releaseDateValueLabel.text = date
            } else {
                print("[AboutUs] 无发布日期数据，显示默认文本")
                releaseDateValueLabel.text = "暂无发布日期"
            }
            toggleReleaseDateRow(hidden: false)
            
            // 不手动更新按钮约束，保持按钮与versionInfoContainer底部的相对位置
            // 由于versionInfoContainer底部与releaseDateRow绑定，所以当toggleReleaseDateRow切换时
            // 按钮位置会自动跟随调整，保持25pt的间距
            
        } else {
            // 已是最新
            latestVersionValueLabel.text = "v\(latestVersion)"
            latestVersionValueLabel.textColor = UIColor.darkGray

            updateButton.isUserInteractionEnabled = false // 禁止点击但保持配色
            updateButton.isEnabled = true // 避免系统置灰

            var config = updateButton.configuration
            config?.title = "当前是最新版本"
            config?.baseBackgroundColor = UIColor(hex: "#FF8D1A").withAlphaComponent(0.8)
            updateButton.configuration = config

            // 隐藏发布日期相关 UI
            toggleReleaseDateRow(hidden: true)

            // 按钮与最新版本信息保持 25pt 间距
            updateButtonTopConstraint?.update(offset: 25)
        }
        // 更新布局
        view.setNeedsLayout()
        view.layoutIfNeeded()
    }

    private func updateUIForLatestVersion(isLatest: Bool) {
        // 当接口失败或无更新信息时，认为当前就是最新
        if let currentVersionText = currentVersionValueLabel.text?.replacingOccurrences(of: "v", with: "") {
            updateUI(latestVersion: currentVersionText, hasUpdate: !isLatest, releaseDate: nil)
        }
    }

    // MARK: - 辅助：版本号比较
    private func isVersion(_ v1: String, lessThan v2: String) -> Bool {
        let components1 = v1.split(separator: ".").compactMap { Int($0) }
        let components2 = v2.split(separator: ".").compactMap { Int($0) }
        let maxCount = max(components1.count, components2.count)
        for i in 0..<maxCount {
            let part1 = i < components1.count ? components1[i] : 0
            let part2 = i < components2.count ? components2[i] : 0
            if part1 < part2 { return true }
            if part1 > part2 { return false }
        }
        return false
    }

    private func toggleReleaseDateRow(hidden: Bool) {
        print("[AboutUs] 切换发布日期行显示状态: hidden=\(hidden)")
        releaseDateLabel.isHidden = hidden
        releaseDateValueLabel.isHidden = hidden
        releaseDateRow?.isHidden = hidden
        separator2?.isHidden = hidden
        separator3?.isHidden = hidden

        // 更新高度约束
        if hidden {
            releaseDateRowHeightConstraint?.update(offset: 0)
        } else {
            releaseDateRowHeightConstraint?.update(offset: 54)
        }
        
        // 记录最终状态
        print("[AboutUs] 发布日期行状态: isHidden=\(releaseDateRow?.isHidden ?? true), height=\(hidden ? 0 : 54)")
    }

    // MARK: - Actions
    private func setupActions() {
        updateButton.addTarget(self, action: #selector(updateTapped), for: .touchUpInside)
        
        // 添加测试功能 - 长按更新按钮触发测试场景
        #if DEBUG
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(testUpdateScenario))
        updateButton.addGestureRecognizer(longPress)
        #endif
    }

    @objc private func updateTapped() {
        print("更新按钮被点击")
        // TODO: 添加更新逻辑，如跳转到App Store
        UIApplication.shared.open(URL(string: "https://apps.apple.com/app/id123456789")!, options: [:], completionHandler: nil)
    }
    
    // 添加一个辅助方法用于测试
    #if DEBUG
    @objc private func testUpdateScenario(_ gesture: UILongPressGestureRecognizer) {
        // 仅在手势开始时执行一次
        if gesture.state == .began {
            print("[AboutUs] 测试更新场景")
            // 模拟有新版本，且有发布日期
            let mockLatestVersion = "2.0.0" // 比当前版本高
            let mockReleaseDate = "2025-07-15 15:30:00"
            
            // 获取当前版本
            guard let currentVersion = currentVersionValueLabel.text?.replacingOccurrences(of: "v", with: "") else {
                return
            }
            
            // 模拟有更新场景
            updateUI(latestVersion: mockLatestVersion, hasUpdate: true, releaseDate: mockReleaseDate)
            
            // 延迟3秒后，模拟无更新场景
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                print("[AboutUs] 测试无更新场景")
                self.updateUI(latestVersion: currentVersion, hasUpdate: false, releaseDate: nil)
                
                // 再延迟3秒，模拟有更新但无发布日期场景
                DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                    print("[AboutUs] 测试有更新但无发布日期场景")
                    self.updateUI(latestVersion: mockLatestVersion, hasUpdate: true, releaseDate: "")
                }
            }
        }
    }
    #endif

    @objc private func userAgreementTapped() {
        print("用户协议被点击")
        // 导航到用户协议页面
        let webView = WebViewController(url: URL(string: "https://gzyoushu.com/privacy/ysh-docuser.htm")!,title: " ")
        navigationController?.pushViewController(webView, animated: true)
    }

    @objc private func privacyPolicyTapped() {
        print("隐私政策被点击")
        // 导航到隐私政策页面
        let webView = WebViewController(url: URL(string: "https://gzsxq.com/rule/sxq-privacy-policy.html")!,title: " ")
        navigationController?.pushViewController(webView, animated: true)
    }

    @objc private func contactSupportTapped() {
        print("联系客服被点击")
        showContactSupportSheet()
    }

    // MARK: - Helper Factories for Labels
    static func createLeftLabel(text: String) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .boldSystemFont(ofSize: 16)
        label.textColor = UIColor.gray
        return label
    }

    static func createRightLabel(text: String, color: UIColor = UIColor(hex: "#707070")) -> UILabel {
        let label = UILabel()
        label.text = text
        label.font = .boldSystemFont(ofSize: 14)
        label.textColor = color
        label.textAlignment = .right
        return label
    }

    // MARK: - Contact Support Sheet
    private var contactSupportSheet: UIView?
    private var overlayView: UIControl?
    private var supportSheetBottomConstraint: Constraint?

    private func showContactSupportSheet() {
        // 若已显示则直接返回
        guard contactSupportSheet == nil else { return }

        // 半透明背景，用于点击关闭
        let overlay = UIControl()
        overlay.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        overlay.alpha = 0
        overlay.addTarget(self, action: #selector(dismissContactSupportSheet), for: .touchUpInside)

        view.addSubview(overlay)
        overlay.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // 底部弹框
        let sheet = UIView()
        sheet.backgroundColor = .white
        sheet.layer.cornerRadius = 12
        sheet.layer.masksToBounds = true
        if #available(iOS 11.0, *) {
            sheet.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        }

        // 服务热线按钮
        let phoneButton = UIButton(type: .system)
        phoneButton.setTitle("服务热线 18924082214", for: .normal)
        phoneButton.setTitleColor(.black, for: .normal)
        phoneButton.titleLabel?.font = .boldSystemFont(ofSize: 16)
        phoneButton.contentHorizontalAlignment = .left
        phoneButton.addTarget(self, action: #selector(callServiceHotline), for: .touchUpInside)

        sheet.addSubview(phoneButton)
        phoneButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.right.equalToSuperview().offset(-16)
            make.top.equalToSuperview().offset(20)
            make.height.equalTo(40)
        }

        view.addSubview(sheet)

        // 计算高度：80pt + safe area 底部
        let sheetHeight: CGFloat = 80 + view.safeAreaInsets.bottom
        var bottomConstraint: Constraint!
        sheet.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(sheetHeight)
            bottomConstraint = make.bottom.equalToSuperview().offset(sheetHeight).constraint
        }

        // 保存引用
        contactSupportSheet = sheet
        overlayView = overlay
        supportSheetBottomConstraint = bottomConstraint

        view.layoutIfNeeded()

        // 动画显示
        UIView.animate(withDuration: 0.25) {
            overlay.alpha = 1
            bottomConstraint.update(offset: 0)
            self.view.layoutIfNeeded()
        }
    }

    @objc private func dismissContactSupportSheet() {
        guard let sheet = contactSupportSheet, let overlay = overlayView, let bottomConstraint = supportSheetBottomConstraint else { return }

        UIView.animate(withDuration: 0.25, animations: {
            overlay.alpha = 0
            // 将 sheet 移回屏幕外
            bottomConstraint.update(offset: sheet.frame.height)
            self.view.layoutIfNeeded()
        }) { _ in
            sheet.removeFromSuperview()
            overlay.removeFromSuperview()
            self.contactSupportSheet = nil
            self.overlayView = nil
            self.supportSheetBottomConstraint = nil
        }
    }

    @objc private func callServiceHotline() {
        let phoneNumber = "18924082214"

        // 创建电话URL
        guard let phoneURL = URL(string: "tel://\(phoneNumber)") else {
            print("无效的电话号码")
            return
        }

        // 检查设备是否支持拨打电话
        if UIApplication.shared.canOpenURL(phoneURL) {
            // 显示系统电话确认弹窗
            UIApplication.shared.open(phoneURL, options: [:]) { success in
                if success {
                    print("成功呼出电话弹窗")
                } else {
                    print("呼出电话失败")
                }
            }
        } else {
            // 设备不支持拨打电话（如iPad），显示提示
            let alert = UIAlertController(title: "无法拨打电话", message: "当前设备不支持拨打电话功能", preferredStyle: .alert)
            alert.addAction(UIAlertAction(title: "确定", style: .default))
            present(alert, animated: true)
        }

        // 关闭弹窗
        dismissContactSupportSheet()
    }
}

