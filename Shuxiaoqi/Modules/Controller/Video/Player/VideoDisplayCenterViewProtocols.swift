//
//  VideoDisplayCenterViewProtocols.swift
//  Shu<PERSON><PERSON>qi
//
//  Created by yong<PERSON><PERSON> ye on 2025/3/26.
//

/**
 播放器模块 - 协议集合
 
 该文件通过协议（Protocol）定义了播放器模块内部/外部通信契约，促成松耦合设计。
 
 - `VideoPageDelegate`  
   用于 `VideoPage` -> 外部 (通常是 `VideoDisplayCenterViewController`) 的事件回调，目前仅声明分享按钮点击事件，可扩展更多交互。
 
 若后续需要新增页面事件（如点赞、关注），建议统一在此文件新增协议方法，以保持模块解耦和代码集中管理。
 */
import Foundation

// MARK: - VideoPage代理协议
protocol VideoPageDelegate: AnyObject {
    /// 当用户点击分享按钮时调用
    func videoPageDidTapShare(_ page: VideoPage)
} 