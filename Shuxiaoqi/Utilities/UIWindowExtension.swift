//
//  UIWindowExtension.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/3/20.
//

import UIKit

// 窗口工具类，用于安全获取窗口和安全区域
class WindowUtil {
    
    // 获取当前活跃的窗口
    static var currentWindow: UIWindow? {
        // iOS 15及以上使用UIWindowScene.windows
        return UIApplication.shared.connectedScenes
            .filter { $0.activationState == .foregroundActive }
            .first(where: { $0 is UIWindowScene })
            .flatMap({ $0 as? UIWindowScene })?.windows
            .first(where: { $0.isKeyWindow })
    }
    
    // 获取安全区域顶部高度（状态栏高度）
    static var safeAreaTop: CGFloat {
        return currentWindow?.safeAreaInsets.top ?? 0
    }
    
    // 获取安全区域底部高度（底部小黑条高度）
    static var safeAreaBottom: CGFloat {
        return currentWindow?.safeAreaInsets.bottom ?? 0
    }
    
    // 获取安全区域左侧宽度
    static var safeAreaLeft: CGFloat {
        return currentWindow?.safeAreaInsets.left ?? 0
    }
    
    // 获取安全区域右侧宽度
    static var safeAreaRight: CGFloat {
        return currentWindow?.safeAreaInsets.right ?? 0
    }
    
    // 获取完整的安全区域
    static var safeAreaInsets: UIEdgeInsets {
        return currentWindow?.safeAreaInsets ?? .zero
    }
    
    // 获取状态栏高度
    static var statusBarHeight: CGFloat {
        return safeAreaTop
    }
    
    // 获取导航栏高度（包含状态栏）
    static var navigationBarHeight: CGFloat {
        return statusBarHeight + 44
    }
    
    // 获取TabBar高度（包含底部安全区域）
    static var tabBarHeight: CGFloat {
        return 49 + safeAreaBottom
    }
    
    // 获取屏幕宽度
    static var screenWidth: CGFloat {
        return UIScreen.main.bounds.width
    }
    
    // 获取屏幕高度
    static var screenHeight: CGFloat {
        return UIScreen.main.bounds.height
    }
} 