import Foundation
import UIKit

/// 代表一段 @提及 的范围及其映射 id
struct MentionRange {
    let id: String
    let nickname: String
    let range: NSRange // 在替换后 displayText 中的 UTF16 范围
}

/// 工具：将原始 commentDesc 与 mentionedUser 映射解析，得到用于展示的文本与范围信息
/// - Parameters:
///   - rawText: 原始 commentDesc，例如 "@ff808081...谢谢"
///   - mapping: API 返回的 mentionedUser 字典，key=id，value=nickname
/// - Returns: (displayText, mentionRanges)
func parseMentions(in rawText: String, mapping: [String: String]) -> (String, [MentionRange]) {
    guard !mapping.isEmpty else { return (rawText, []) }

    // 正则：@ + id(允许字母数字连字符) + 可选单个反斜杠（接口容错带的）
    let pattern = "@([A-Za-z0-9_-]+)\\\\?"
    guard let regex = try? NSRegularExpression(pattern: pattern, options: []) else {
        return (rawText, [])
    }

    // 先收集所有匹配项和对应的替换信息，按位置从前到后排序
    let matches = regex.matches(in: rawText, options: [], range: NSRange(location: 0, length: rawText.utf16.count))
    var replacements: [(range: NSRange, id: String, nickname: String, displayToken: String)] = []

    for match in matches {
        let idRange = match.range(at: 1) // 捕获到的 id
        guard let idSubstringRange = Range(idRange, in: rawText) else { continue }
        let id = String(rawText[idSubstringRange])
        guard let nickname = mapping[id] else { continue }
        let displayToken = "@" + nickname
        replacements.append((range: match.range, id: id, nickname: nickname, displayToken: displayToken))
    }

    // 如果没有有效的替换，直接返回原文本
    guard !replacements.isEmpty else { return (rawText, []) }

    // 使用字符串构建方式，避免range计算错误
    var result = rawText
    var mentionRanges: [MentionRange] = []
    var offset = 0 // 累计偏移量

    // 从前往后处理每个替换
    for replacement in replacements {
        // 计算当前替换在结果字符串中的实际位置
        let adjustedLocation = replacement.range.location + offset
        let adjustedRange = NSRange(location: adjustedLocation, length: replacement.range.length)

        // 执行替换
        let nsResult = NSMutableString(string: result)
        nsResult.replaceCharacters(in: adjustedRange, with: replacement.displayToken)
        result = nsResult as String

        // 计算替换后的新range
        let newRange = NSRange(location: adjustedLocation, length: replacement.displayToken.utf16.count)
        mentionRanges.append(MentionRange(id: replacement.id, nickname: replacement.nickname, range: newRange))

        // 更新偏移量：新长度 - 原长度
        offset += replacement.displayToken.utf16.count - replacement.range.length
    }

    return (result, mentionRanges)
}