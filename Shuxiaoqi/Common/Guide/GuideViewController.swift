import UIKit
import SnapKit

class GuideViewController: UIViewController {
    
    // MARK: - Properties
    
    private let scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.isPagingEnabled = true
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.bounces = false
        return scrollView
    }()
    
    private let pageControl: UIPageControl = {
        let pageControl = UIPageControl()
        pageControl.numberOfPages = 3
        pageControl.currentPage = 0
        pageControl.currentPageIndicatorTintColor = UIColor(hex: "#FF8F1F")
        pageControl.pageIndicatorTintColor = UIColor(hex: "#CCCCCC")
        
        // 通过调整transform来设置点的大小
        let transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        pageControl.transform = transform
        
        return pageControl
    }()
    
    private let startButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("立即体验", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = UIColor(hex: "#FF8F1F")
        button.layer.cornerRadius = 21.5 // 43/2
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.alpha = 0
        return button
    }()
    
    // 添加布局常量结构体
    private struct LayoutMetrics {
        static let standardScreenHeight: CGFloat = 812 // iPhone X 屏幕高度作为基准
        static let baseDescriptionSpacing: CGFloat = 165 // 基准间距
        static let pageControlTopSpacing: CGFloat = 30 // 页面指示器距离安全区域顶部间距
        static let descriptionHeight: CGFloat = 46 // 描述文本高度
        static let descriptionSideMargin: CGFloat = 35 // 描述文本左右间距
        
        // 计算描述文本到页面指示器的动态间距
        static func calculateDescriptionSpacing(for screenHeight: CGFloat) -> CGFloat {
            let ratio = screenHeight / standardScreenHeight
            // 尝试更强的缩放因子 (ratio^3)，使小屏幕上间距缩减更多
            let adjustedRatio = pow(ratio, 3.0)
            // 也可以考虑添加一个最小间距保护，如果需要的话
            // 例如：let minSpacing: CGFloat = 80
            // return max(baseDescriptionSpacing * adjustedRatio, minSpacing)
            return baseDescriptionSpacing * adjustedRatio
        }
    }
    
    // 引导页数据
    private let guideData: [(image: String, title: String, description: String)] = [
        ("guide_1", "品质购物", "发现独特好物，享受品质生活。海量商品，一站式购物体验，让您轻松获得心仪之选。"),
        ("guide_2", "精彩直播", "沉浸式短视频体验，精彩直播互动。 发现更多有趣内容，让娱乐触手可及。"),
        ("guide_3", "快捷配送", "美食外卖，专业配送。多样化餐饮选择，专业配送团队，让您足不出户享受美味。")
    ]
    
    // MARK: - Lifecycle
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }
    
    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        
        // 调整页面指示器点的间距
        let dots = pageControl.subviews.filter {
            String(describing: type(of: $0)).contains("PageControlContentView")
        }.first?.subviews ?? []
        
        if !dots.isEmpty {
            let spacing: CGFloat = 8.0
            let total = CGFloat(dots.count - 1) * spacing
            for (index, dot) in dots.enumerated() {
                if index == 0 {
                    dot.frame.origin.x = dot.frame.origin.x - total / 2
                } else {
                    dot.frame.origin.x = dots[index-1].frame.maxX + spacing
                }
            }
        }
    }
    
    // MARK: - UI Setup
    
    private func setupUI() {
        view.backgroundColor = .white
        
        // 添加滚动视图
        view.addSubview(scrollView)
        scrollView.delegate = self
        
        // 添加引导页内容
        for (index, data) in guideData.enumerated() {
            let pageView = createPageView(
                image: data.image,
                title: data.title,
                description: data.description,
                index: index
            )
            scrollView.addSubview(pageView)
        }
        
        // 添加页面指示器
        view.addSubview(pageControl)
        
        // 添加开始按钮
        view.addSubview(startButton)
        startButton.addTarget(self, action: #selector(startButtonTapped), for: .touchUpInside)
    }
    
    private func setupConstraints() {
        // 滚动视图约束
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // 设置内容大小
        scrollView.contentSize = CGSize(
            width: UIScreen.main.bounds.width * CGFloat(guideData.count),
            height: UIScreen.main.bounds.height
        )
        
        // 页面指示器约束 - 距离底部安全区域上方30pt
        pageControl.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-30)
        }
        
        // 开始按钮约束 - 180x43
        startButton.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(pageControl.snp.top).offset(-20)
            make.width.equalTo(180)
            make.height.equalTo(43)
        }
    }
    
    private func createPageView(image: String, title: String, description: String, index: Int) -> UIView {
        let pageView = UIView()
        let screenWidth = UIScreen.main.bounds.width
        let screenHeight = UIScreen.main.bounds.height
        
        // 设置页面frame
        pageView.frame = CGRect(
            x: screenWidth * CGFloat(index),
            y: 0,
            width: screenWidth,
            height: screenHeight
        )
        
        // 创建图片视图
        let imageView = UIImageView()
        imageView.image = UIImage(named: image)
        imageView.contentMode = .scaleAspectFit
        pageView.addSubview(imageView)
        
        // 创建标题标签
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.textAlignment = .center
        titleLabel.font = .systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = UIColor(hex: "#000000", alpha: 0.85)
        pageView.addSubview(titleLabel)
        
        // 创建描述标签
        let descLabel = UILabel()
        descLabel.text = description
        descLabel.textAlignment = .center
        descLabel.font = .systemFont(ofSize: 16)
        descLabel.textColor = UIColor(hex: "#000000", alpha: 0.65)
        descLabel.numberOfLines = 0
        pageView.addSubview(descLabel)
        
        // 计算动态间距
        let dynamicSpacing = LayoutMetrics.calculateDescriptionSpacing(for: screenHeight)
        
        // 计算从页面底部到描述标签底部的距离
        // pageControl距底部安全区域30pt + startButton高度43pt + startButton到pageControl的间距20pt + 描述标签到startButton的间距
        let bottomOffset = -(30 + 43 + 20 + dynamicSpacing)
        
        // 设置约束
        descLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(LayoutMetrics.descriptionSideMargin)
            make.bottom.equalTo(pageView).offset(bottomOffset)
            make.height.equalTo(LayoutMetrics.descriptionHeight)
        }
        
        // 标题约束
        titleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.left.right.equalToSuperview().inset(35)
            make.bottom.equalTo(descLabel.snp.top).offset(-16)
        }
        
        // 图片约束 - 根据不同页面设置不同的约束
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            
            // 根据页面索引设置不同的约束
            switch index {
            case 0: // 第一页图片
                make.bottom.equalTo(titleLabel.snp.top).offset(-56)
                make.width.equalTo(375 * (screenWidth / 375)) // 原始宽度375pt，根据屏幕宽度比例缩放
                make.height.equalTo(247 * (screenWidth / 375)) // 原始高度247pt，保持宽高比
            case 1: // 第二页图片
                make.bottom.equalTo(titleLabel.snp.top).offset(-54)
                make.width.equalTo(375 * (screenWidth / 375))
                make.height.equalTo(290 * (screenWidth / 375))
            case 2: // 第三页图片
                make.bottom.equalTo(titleLabel.snp.top).offset(-63)
                make.width.equalTo(375 * (screenWidth / 375))
                make.height.equalTo(245 * (screenWidth / 375))
            default:
                // 默认情况
                make.bottom.equalTo(titleLabel.snp.top).offset(-56)
                make.width.equalTo(screenWidth)
                make.height.equalTo(imageView.snp.width).multipliedBy(0.7)
            }
        }
        
        return pageView
    }
    
    // MARK: - Actions
    
    @objc private func startButtonTapped() {
        // 进入主页面并检查用户协议
        if let sceneDelegate = UIApplication.shared.connectedScenes.first?.delegate as? SceneDelegate {
            sceneDelegate.setupMainInterface()

            // 延迟一点时间确保界面已经完全加载，然后检查用户协议
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                UserAgreementManager.shared.checkAndShowAgreementIfNeeded { accepted in
                    if !accepted {
                        // 用户不同意协议，应用将退出
                        print("[GuideViewController] 用户不同意用户协议，应用即将退出")
                    }
                }
            }
        }
    }
}

// MARK: - UIScrollViewDelegate

extension GuideViewController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        let page = Int(round(scrollView.contentOffset.x / view.bounds.width))
        pageControl.currentPage = page
        
        // 在最后一页显示开始按钮
        UIView.animate(withDuration: 0.3) {
            self.startButton.alpha = page == self.guideData.count - 1 ? 1 : 0
        }
    }
}
