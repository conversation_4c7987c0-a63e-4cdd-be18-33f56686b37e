//
//  NotificationPopSelector.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/6/6.
//

import UIKit

// MARK: - 通用底部选择器弹窗
class NotificationPopSelector: UIView {
    struct Option {
        let title: String
        let isSelected: Bool
    }
    private var options: [Option] = []
    private var selectHandler: ((Int) -> Void)?
    private let backgroundMaskView = UIView()
    private let containerView = UIView()
    private let topBar = UIView()
    private let optionContainer = UIView()
    private let stackView = UIStackView()
    private var selectedIndex: Int = 0
    init(options: [String], selectedIndex: Int, selectHandler: ((Int) -> Void)?) {
        super.init(frame: UIScreen.main.bounds)
        self.options = options.enumerated().map { Option(title: $0.element, isSelected: $0.offset == selectedIndex) }
        self.selectedIndex = selectedIndex
        self.selectHandler = selectHandler
        setupUI()
    }
    required init?(coder: NSCoder) { fatalError("init(coder:) has not been implemented") }
    private func setupUI() {
        backgroundColor = .clear
        backgroundMaskView.backgroundColor = UIColor(hex: "#8C8C8C").withAlphaComponent(0.5)
        addSubview(backgroundMaskView)
        backgroundMaskView.frame = bounds
        backgroundMaskView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        let tap = UITapGestureRecognizer(target: self, action: #selector(dismissSelf))
        backgroundMaskView.addGestureRecognizer(tap)
        addSubview(containerView)
        containerView.backgroundColor = UIColor(hex: "#EFEFEF")
        containerView.layer.cornerRadius = 10
        containerView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        containerView.clipsToBounds = true
        topBar.backgroundColor = UIColor(hex: "#D8D8D8")
        topBar.layer.cornerRadius = 2
        containerView.addSubview(topBar)
        optionContainer.backgroundColor = .white
        optionContainer.layer.cornerRadius = 5
        optionContainer.clipsToBounds = true
        containerView.addSubview(optionContainer)
        stackView.axis = .vertical
        stackView.spacing = 0
        optionContainer.addSubview(stackView)
        for (i, opt) in options.enumerated() {
            let row = UIButton(type: .system)
            row.setTitle(opt.title, for: .normal)
            row.setTitleColor(UIColor(hex: "#333333"), for: .normal)
            row.titleLabel?.font = .systemFont(ofSize: 14)
            row.contentHorizontalAlignment = .left
            row.backgroundColor = .white
            row.tag = i
            row.addTarget(self, action: #selector(optionTapped(_:)), for: .touchUpInside)
            row.titleEdgeInsets = UIEdgeInsets(top: 0, left: 10, bottom: 0, right: 0)
            if opt.isSelected {
                let check = UIImageView(image: UIImage(named: "user_info_edit_list_select"))
                check.contentMode = .scaleAspectFit
                row.addSubview(check)
                check.translatesAutoresizingMaskIntoConstraints = false
                NSLayoutConstraint.activate([
                    check.centerYAnchor.constraint(equalTo: row.centerYAnchor),
                    check.rightAnchor.constraint(equalTo: row.rightAnchor, constant: -20),
                    check.widthAnchor.constraint(equalToConstant: 20),
                    check.heightAnchor.constraint(equalToConstant: 20)
                ])
            }
            if i > 0 {
                let sep = UIView()
                sep.backgroundColor = UIColor(hex: "#F2F2F2")
                sep.translatesAutoresizingMaskIntoConstraints = false
                row.addSubview(sep)
                NSLayoutConstraint.activate([
                    sep.leftAnchor.constraint(equalTo: row.leftAnchor, constant: 0),
                    sep.rightAnchor.constraint(equalTo: row.rightAnchor),
                    sep.topAnchor.constraint(equalTo: row.topAnchor),
                    sep.heightAnchor.constraint(equalToConstant: 1)
                ])
            }
            stackView.addArrangedSubview(row)
            row.heightAnchor.constraint(equalToConstant: 41.75).isActive = true
        }
    }
    override func layoutSubviews() {
        super.layoutSubviews()
        let safe = safeAreaInsets.bottom
        let contentH: CGFloat = 218 + safe
        containerView.frame = CGRect(x: 0, y: bounds.height, width: bounds.width, height: contentH)
        topBar.frame = CGRect(x: (bounds.width-36)/2, y: 8, width: 36, height: 4)
        let optionW = bounds.width - 24
        optionContainer.frame = CGRect(x: 12, y: 25, width: optionW, height: 167)
        stackView.frame = CGRect(x: 0, y: 0, width: optionW, height: 167)
    }
    public func show(in parent: UIView) {
        parent.addSubview(self)
        layoutIfNeeded()
        let safe = safeAreaInsets.bottom
        let contentH: CGFloat = 218 + safe
        UIView.animate(withDuration: 0.25) {
            self.containerView.frame.origin.y = self.bounds.height - contentH
        }
    }
    @objc private func dismissSelf() {
        UIView.animate(withDuration: 0.2, animations: {
            self.containerView.frame.origin.y = self.bounds.height
        }) { _ in
            self.removeFromSuperview()
        }
    }
    @objc private func optionTapped(_ sender: UIButton) {
        selectHandler?(sender.tag)
        dismissSelf()
    }
}
