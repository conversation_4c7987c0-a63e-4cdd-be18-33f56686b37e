import UIKit

/// A lightweight popup view with optional title, message and a single confirmation button.
/// Usage:
///     InfoPopupView.show(in: someView, title: "标题", message: "提示信息")
/// If `in` view is nil, it will automatically add itself to the key window.
final class InfoPopupView: UIView {
    
    // MARK: - UI Elements
    private let container = UIView()
    private let titleLabel = UILabel()
    private let messageLabel = UILabel()
    private let actionButton = UIButton(type: .system)
    
    // MARK: - Properties
    private var completion: (() -> Void)?
    
    // MARK: - Initialiser
    private init(title: String?,
                 message: String,
                 buttonTitle: String = "我知道了",
                 buttonColor: UIColor = UIColor(hex: "#FF8F1F"),
                 completion: (() -> Void)? = nil) {
        super.init(frame: .zero)
        self.completion = completion
        setupUI(title: title,
                message: message,
                buttonTitle: buttonTitle,
                buttonColor: buttonColor)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Public API
    /// Presents the popup on the given view (or key window if `view` is nil).
    /// - Parameters:
    ///   - view: Target view to host popup. Defaults to key window.
    ///   - title: Optional popup title. Pass `nil` or empty string to hide.
    ///   - message: Message text.
    ///   - buttonTitle: Title of confirmation button. Default "我知道了".
    ///   - buttonColor: Background color of confirmation button.
    ///   - completion: Callback executed after popup is dismissed.
    static func show(in view: UIView? = nil,
                     title: String? = nil,
                     message: String,
                     buttonTitle: String = "我知道了",
                     buttonColor: UIColor = UIColor(hex: "#FF8F1F"),
                     completion: (() -> Void)? = nil) {
        guard let target = view ?? UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else { return }
        let popup = InfoPopupView(title: title,
                                  message: message,
                                  buttonTitle: buttonTitle,
                                  buttonColor: buttonColor,
                                  completion: completion)
        popup.frame = target.bounds
        popup.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        target.addSubview(popup)
    }
    
    // MARK: - UI Setup
    private func setupUI(title: String?,
                         message: String,
                         buttonTitle: String,
                         buttonColor: UIColor) {
        backgroundColor = UIColor.black.withAlphaComponent(0.45)
        
        // Configure container
        container.backgroundColor = .white
        container.layer.cornerRadius = 16
        container.clipsToBounds = true
        addSubview(container)
        container.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            container.centerXAnchor.constraint(equalTo: centerXAnchor),
            container.centerYAnchor.constraint(equalTo: centerYAnchor),
            container.widthAnchor.constraint(equalToConstant: 260)
        ])
        
        // Configure title label
        titleLabel.font = .boldSystemFont(ofSize: 16)
        titleLabel.textColor = UIColor(hex: "#333333")
        titleLabel.textAlignment = .center
        titleLabel.numberOfLines = 0
        
        // Configure message label
        messageLabel.font = .systemFont(ofSize: 15)
        messageLabel.textColor = UIColor(hex: "#666666")
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        
        // Configure action button
        actionButton.setTitleColor(.white, for: .normal)
        actionButton.titleLabel?.font = .boldSystemFont(ofSize: 14)
        actionButton.backgroundColor = buttonColor
        actionButton.layer.cornerRadius = 17.5
        actionButton.clipsToBounds = true
        actionButton.addTarget(self, action: #selector(okButtonTapped), for: .touchUpInside)
        
        // Assign texts
        titleLabel.text = title
        messageLabel.text = message
        actionButton.setTitle(buttonTitle, for: .normal)
        
        // Add subviews
        [titleLabel, messageLabel, actionButton].forEach { subview in
            container.addSubview(subview)
            subview.translatesAutoresizingMaskIntoConstraints = false
        }
        
        // Layout constraints
        if let title = title, !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            NSLayoutConstraint.activate([
                titleLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 24),
                titleLabel.leftAnchor.constraint(equalTo: container.leftAnchor, constant: 20),
                titleLabel.rightAnchor.constraint(equalTo: container.rightAnchor, constant: -20),
                
                messageLabel.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 16)
            ])
        } else {
            titleLabel.isHidden = true
            NSLayoutConstraint.activate([
                messageLabel.topAnchor.constraint(equalTo: container.topAnchor, constant: 24)
            ])
        }
        
        NSLayoutConstraint.activate([
            messageLabel.leftAnchor.constraint(equalTo: container.leftAnchor, constant: 20),
            messageLabel.rightAnchor.constraint(equalTo: container.rightAnchor, constant: -20),
            
            actionButton.topAnchor.constraint(equalTo: messageLabel.bottomAnchor, constant: 24),
            actionButton.leftAnchor.constraint(equalTo: container.leftAnchor, constant: 32),
            actionButton.rightAnchor.constraint(equalTo: container.rightAnchor, constant: -32),
            actionButton.heightAnchor.constraint(equalToConstant: 35),
            actionButton.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -20)
        ])
    }
    
    // MARK: - Actions
    @objc private func okButtonTapped() {
        removeFromSuperview()
        completion?()
    }
} 