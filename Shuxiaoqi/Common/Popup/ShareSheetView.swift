//
//  ShareSheetView.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/5/29.
//

import UIKit

class ShareSheetView: UIView {
    // 蒙版
    private let backgroundMaskView: UIView = {
        let v = UIView()
        v.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        v.alpha = 0
        return v
    }()
    // 弹窗
    private let containerView: UIView = {
        let v = UIView()
        v.backgroundColor = .white
        v.layer.cornerRadius = 16
        v.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        v.clipsToBounds = true
        if #available(iOS 13.0, *) {
            v.backgroundColor = UIColor { trait in
                trait.userInterfaceStyle == .dark ? UIColor.systemBackground : .white
            }
        }
        return v
    }()
    // 标题
    private let titleLabel: UILabel = {
        let l = UILabel()
        l.text = "分享至"
        l.font = UIFont.boldSystemFont(ofSize: 16)
        l.textAlignment = .center
        l.textColor = UIColor(hex: "#222222")
        return l
    }()
    // 关闭按钮
    private let closeButton: UIButton = {
        let btn = UIButton(type: .custom)
        btn.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
        btn.tintColor = UIColor.gray
        return btn
    }()
    // 分享按钮
    private let wechatButton = ShareSheetView.makeShareButton(title: "微信好友", imageName: "share_sheet_wx")
    private let momentsButton = ShareSheetView.makeShareButton(title: "朋友圈", imageName: "share_sheet_pyq")
    private let copyButton = ShareSheetView.makeShareButton(title: "复制链接", imageName: "share_sheet_copy_url")
    
    // 回调（分享后通知）
    var onWeChat: (() -> Void)?
    var onMoments: (() -> Void)?
    var onCopy: (() -> Void)?
    
    // 可选：自定义具体分享行为（若不设置则走默认实现）
    var shareToWeChatAction: (() -> Void)?
    var shareToMomentsAction: (() -> Void)?
    var copyLinkAction: (() -> Void)?

    // 视频分享统计相关
    var currentVideoId: Int?
    var onShareStatisticsSuccess: (() -> Void)?
    var onOptimisticShareUpdate: (() -> Void)?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupActions()
    }
    required init?(coder: NSCoder) { fatalError() }
    
    private func setupUI() {
        // 不在这里设置frame，在show方法中设置
        addSubview(backgroundMaskView)
        addSubview(containerView)

        // 设置背景遮罩
        backgroundMaskView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            backgroundMaskView.topAnchor.constraint(equalTo: topAnchor),
            backgroundMaskView.leadingAnchor.constraint(equalTo: leadingAnchor),
            backgroundMaskView.trailingAnchor.constraint(equalTo: trailingAnchor),
            backgroundMaskView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])

        // 弹窗布局
        let height: CGFloat = 160 + WindowUtil.safeAreaBottom
        containerView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            containerView.leadingAnchor.constraint(equalTo: leadingAnchor),
            containerView.trailingAnchor.constraint(equalTo: trailingAnchor),
            containerView.bottomAnchor.constraint(equalTo: bottomAnchor),
            containerView.heightAnchor.constraint(equalToConstant: height)
        ])
        // 标题和关闭
        containerView.addSubview(titleLabel)
//        containerView.addSubview(closeButton)
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
//        closeButton.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            titleLabel.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            titleLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
//            closeButton.centerYAnchor.constraint(equalTo: titleLabel.centerYAnchor),
//            closeButton.rightAnchor.constraint(equalTo: containerView.rightAnchor, constant: -12),
//            closeButton.widthAnchor.constraint(equalToConstant: 28),
//            closeButton.heightAnchor.constraint(equalToConstant: 28)
        ])
        // 分享按钮
        let stack = UIStackView(arrangedSubviews: [wechatButton, momentsButton, copyButton])
        stack.axis = .horizontal
        stack.alignment = .top
        stack.distribution = .equalSpacing
        stack.spacing = 35
        containerView.addSubview(stack)
        stack.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            stack.leftAnchor.constraint(equalTo: containerView.leftAnchor, constant: 32),
            stack.topAnchor.constraint(equalTo: titleLabel.bottomAnchor, constant: 18),
            stack.heightAnchor.constraint(equalToConstant: 70)
        ])
        // 安全距离空白
        let safeAreaView = UIView()
        safeAreaView.backgroundColor = .clear
        containerView.addSubview(safeAreaView)
        safeAreaView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            safeAreaView.leftAnchor.constraint(equalTo: containerView.leftAnchor),
            safeAreaView.rightAnchor.constraint(equalTo: containerView.rightAnchor),
            safeAreaView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            safeAreaView.heightAnchor.constraint(equalToConstant: WindowUtil.safeAreaBottom)
        ])
    }
    
    private func setupActions() {
        let tap = UITapGestureRecognizer(target: self, action: #selector(dismissSelf))
        backgroundMaskView.addGestureRecognizer(tap)
        closeButton.addTarget(self, action: #selector(dismissSelf), for: .touchUpInside)
        wechatButton.addTarget(self, action: #selector(wechatTapped), for: .touchUpInside)
        momentsButton.addTarget(self, action: #selector(momentsTapped), for: .touchUpInside)
        copyButton.addTarget(self, action: #selector(copyTapped), for: .touchUpInside)
    }
    
    @objc private func dismissSelf() {
        print("=== 关闭分享弹窗 ===")
        UIView.animate(withDuration: 0.25, animations: {
            self.backgroundMaskView.alpha = 0
            self.containerView.transform = CGAffineTransform(translationX: 0, y: 160 + WindowUtil.safeAreaBottom)
        }) { _ in
            print("分享弹窗关闭完成")
            self.removeFromSuperview()
        }
    }
    @objc private func wechatTapped() {
        // 立即进行乐观更新
        performOptimisticShareUpdate()

        // 调用分享统计API
        callShareStatisticsAPI()

        // 检查微信是否可用
        guard WXApi.isWXAppInstalled() else {
            print("微信未安装")
            return
        }

        guard WXApi.isWXAppSupport() else {
            print("微信版本不支持API")
            return
        }

        if let custom = shareToWeChatAction {
            print("执行自定义微信分享")
            custom()
        } else {
            print("执行默认微信分享")
            // 微信好友分享网页测试（默认实现）
            let webpageObject = WXWebpageObject()
            webpageObject.webpageUrl = "https://www.shuxiaoqi.com"
            let message = WXMediaMessage()
            message.mediaObject = webpageObject
            message.title = "这是一个漂亮的页面"
            message.description = "滴滴滴，来"
            if let image = UIImage(named: "guide_1") {
                message.setThumbImage(image)
            }
            let req = SendMessageToWXReq()
            req.bText = false
            req.message = message
            req.scene = 0 // 好友
            let result = WXApi.send(req)
            print("微信分享请求发送结果: \(result)")
        }
        onWeChat?()
        dismissSelf()
    }
    @objc private func momentsTapped() {
        // 立即进行乐观更新
        performOptimisticShareUpdate()

        // 调用分享统计API
        callShareStatisticsAPI()

        // 检查微信是否可用
        guard WXApi.isWXAppInstalled() else {
            print("微信未安装")
            return
        }

        guard WXApi.isWXAppSupport() else {
            print("微信版本不支持API")
            return
        }

        if let custom = shareToMomentsAction {
            print("执行自定义朋友圈分享")
            custom()
        } else {
            print("执行默认朋友圈分享")
            // 朋友圈分享网页测试（默认实现）
            let webpageObject = WXWebpageObject()
            webpageObject.webpageUrl = "https://www.baidu.com"
            let message = WXMediaMessage()
            message.mediaObject = webpageObject
            message.title = "测试网页-朋友圈"
            message.description = "这是一个用于测试的朋友圈网页分享"
            if let image = UIImage(named: "guide_1") {
                message.setThumbImage(image)
            }
            let req = SendMessageToWXReq()
            req.bText = false
            req.message = message
            req.scene = 1 // 朋友圈
            let result = WXApi.send(req)
            print("朋友圈分享请求发送结果: \(result)")
        }
        onMoments?()
        dismissSelf()
    }
    @objc private func copyTapped() {
        // 立即进行乐观更新
        performOptimisticShareUpdate()

        // 调用分享统计API
        callShareStatisticsAPI()

        if let custom = copyLinkAction {
            custom()
        } else {
            // 默认：复制测试链接并打印成功状态文案到屏幕上
            UIPasteboard.general.string = "https://test.share.link/abc123"
//            if let url = URL(string: "weixin://"), UIApplication.shared.canOpenURL(url) {
//                UIApplication.shared.open(url)
//            }
        }
        onCopy?()
        dismissSelf()
    }

    // MARK: - 分享统计
    /// 立即进行乐观更新（先更新UI，提升用户体验）
    private func performOptimisticShareUpdate() {
        print("[ShareSheet] 执行乐观分享数更新")
        onOptimisticShareUpdate?()
    }

    /// 调用视频分享统计API
    private func callShareStatisticsAPI() {
        guard let videoId = currentVideoId else {
            print("[ShareSheet] 无法调用分享统计API：视频ID为空")
            return
        }

        print("[ShareSheet] 调用视频分享统计API，视频ID: \(videoId)")
        APIManager.shared.getSvWorkShare(worksId: videoId) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        print("[ShareSheet] 视频分享统计成功，通知更新分享数")
                        self?.onShareStatisticsSuccess?()
                    } else {
                        print("[ShareSheet] 视频分享统计失败: \(response.displayMessage)")
                    }
                case .failure(let error):
                    print("[ShareSheet] 视频分享统计请求失败: \(error.errorMessage)")
                }
            }
        }
    }

    func show(in view: UIView) {
        print("=== 显示分享弹窗 ===")
        print("父视图尺寸: \(view.bounds)")

        // 设置分享弹窗的frame为父视图的bounds
        self.frame = view.bounds
        view.addSubview(self)

        // 初始状态：容器在底部外面，背景透明
        self.containerView.transform = CGAffineTransform(translationX: 0, y: 160 + WindowUtil.safeAreaBottom)
        self.backgroundMaskView.alpha = 0

        print("开始显示动画")

        UIView.animate(withDuration: 0.25, animations: {
            self.backgroundMaskView.alpha = 1
            self.containerView.transform = .identity
        }) { _ in
            print("分享弹窗显示完成")
        }
    }
    
    // 工厂方法
    static func makeShareButton(title: String, imageName: String) -> UIButton {
        let btn = UIButton(type: .custom)
        let icon = UIImageView()
        icon.image = UIImage(named: imageName)
        icon.backgroundColor = .clear
        icon.layer.cornerRadius = 20
        icon.clipsToBounds = true
        icon.contentMode = .scaleAspectFill
        icon.translatesAutoresizingMaskIntoConstraints = false
        btn.addSubview(icon)
        NSLayoutConstraint.activate([
            icon.topAnchor.constraint(equalTo: btn.topAnchor),
            icon.centerXAnchor.constraint(equalTo: btn.centerXAnchor),
            icon.widthAnchor.constraint(equalToConstant: 40),
            icon.heightAnchor.constraint(equalToConstant: 40)
        ])
        let label = UILabel()
        label.text = title
        label.font = UIFont.systemFont(ofSize: 13)
        label.textColor = UIColor(hex: "#666666")
        label.textAlignment = .center
        label.translatesAutoresizingMaskIntoConstraints = false
        btn.addSubview(label)
        NSLayoutConstraint.activate([
            label.topAnchor.constraint(equalTo: icon.bottomAnchor, constant: 10),
            label.centerXAnchor.constraint(equalTo: btn.centerXAnchor),
            label.bottomAnchor.constraint(equalTo: btn.bottomAnchor)
        ])
        btn.translatesAutoresizingMaskIntoConstraints = false
        btn.widthAnchor.constraint(equalToConstant: 70).isActive = true
        btn.heightAnchor.constraint(equalToConstant: 74).isActive = true
        return btn
    }
}
