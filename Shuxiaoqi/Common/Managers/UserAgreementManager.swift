import UIKit

/// 用户协议管理器
/// 负责处理应用启动时的用户协议确认流程
class UserAgreementManager {
    
    // MARK: - Singleton
    static let shared = UserAgreementManager()
    private init() {}
    
    // MARK: - Constants
    private let userAgreementAcceptedKey = "UserAgreementAccepted"
    
    // MARK: - Properties
    /// 检查用户是否已经同意用户协议
    var hasUserAcceptedAgreement: Bool {
        return UserDefaults.standard.bool(forKey: userAgreementAcceptedKey)
    }
    
    // MARK: - Public Methods
    
    /// 检查并显示用户协议弹窗（如果需要）
    /// - Parameter completion: 完成回调，参数表示用户是否同意协议
    func checkAndShowAgreementIfNeeded(completion: @escaping (Bool) -> Void) {
        // 如果用户已经同意过协议，直接返回
        if hasUserAcceptedAgreement {
            completion(true)
            return
        }
        
        // 显示用户协议弹窗
        showUserAgreementPopup(completion: completion)
    }
    
    /// 强制显示用户协议弹窗
    /// - Parameter completion: 完成回调，参数表示用户是否同意协议
    func showUserAgreementPopup(completion: @escaping (Bool) -> Void) {
        UserAgreementPopupView.show(
            onAgree: { [weak self] in
                // 用户同意协议
                self?.saveUserAgreementAcceptance()
                completion(true)
            },
            onDisagree: { [weak self] in
                // 用户不同意，显示确认弹窗
                self?.showConfirmationPopup(completion: completion)
            }
        )
    }
    
    /// 保存用户协议接受状态
    private func saveUserAgreementAcceptance() {
        UserDefaults.standard.set(true, forKey: userAgreementAcceptedKey)
        UserDefaults.standard.synchronize()
        print("[UserAgreementManager] 用户协议接受状态已保存")
    }
    
    /// 显示确认弹窗（当用户点击不同意时）
    /// - Parameter completion: 完成回调，参数表示用户是否同意协议
    private func showConfirmationPopup(completion: @escaping (Bool) -> Void) {
        UserAgreementConfirmPopupView.show(
            onAgree: { [weak self] in
                // 用户最终同意协议
                self?.saveUserAgreementAcceptance()
                completion(true)
            },
            onExit: {
                // 用户选择放弃使用，退出应用
                completion(false)
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    exit(0)
                }
            }
        )
    }
    
    /// 重置用户协议状态（用于测试）
    func resetUserAgreementStatus() {
        UserDefaults.standard.removeObject(forKey: userAgreementAcceptedKey)
        UserDefaults.standard.synchronize()
        print("[UserAgreementManager] 用户协议状态已重置")
    }
    
    /// 打开用户协议页面
    /// - Parameter from: 当前视图控制器
    func openUserAgreement(from viewController: UIViewController) {
        guard let url = URL(string: "https://gzyoushu.com/privacy/ysh-docuser.htm") else { return }
        let webView = WebViewController(url: url, title: "用户协议")
        if let navigationController = viewController.navigationController {
            navigationController.pushViewController(webView, animated: true)
        } else {
            webView.modalPresentationStyle = .fullScreen
            viewController.present(webView, animated: true)
        }
    }
    
    /// 打开隐私政策页面
    /// - Parameter from: 当前视图控制器
    func openPrivacyPolicy(from viewController: UIViewController) {
        guard let url = URL(string: "https://gzsxq.com/rule/sxq-privacy-policy.html") else { return }
        let webView = WebViewController(url: url, title: "隐私政策")
        if let navigationController = viewController.navigationController {
            navigationController.pushViewController(webView, animated: true)
        } else {
            webView.modalPresentationStyle = .fullScreen
            viewController.present(webView, animated: true)
        }
    }

    /// 测试用户协议弹窗功能
    func testUserAgreementPopup() {
        showUserAgreementPopup { accepted in
            if accepted {
                print("[UserAgreementManager] 测试：用户同意了协议")
            } else {
                print("[UserAgreementManager] 测试：用户不同意协议")
            }
        }
    }
}
