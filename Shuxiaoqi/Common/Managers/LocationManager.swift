import Foundation
import CoreLocation

/// 负责定位、逆地理编码、省市json加载和areaCode匹配的单例
class LocationManager: NSObject, CLLocationManagerDelegate {
    static let shared = LocationManager()
    private override init() { super.init() }

    private var locationManager: CLLocationManager?
    private var completion: ((String?, String?) -> Void)? // areaCode, address
    private var provinceList: [[String: Any]]?
    private var cityList: [[[String: Any]]]?  
    private var cityJsonLoaded = false
    private let provinceJsonUrl = "https://image.gzyoushu.com/ae1e13622e464c2899f53858a2596b56.json"
    private let cityJsonUrl = "https://image.gzyoushu.com/dc571ca6b0cd498d91125ce5a1c02cb7.json"

    // areaCode本地缓存key
    private let areaCodeCacheKey = "lastAreaCode"
    // 省市json本地缓存路径
    private var provinceJsonPath: String {
        let doc = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first!
        return (doc as NSString).appendingPathComponent("area_province.json")
    }
    private var cityJsonPath: String {
        let doc = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first!
        return (doc as NSString).appendingPathComponent("area_city.json")
    }

    // 获取本地缓存的areaCode
    func getCachedAreaCode() -> String? {
        let code = UserDefaults.standard.string(forKey: areaCodeCacheKey)
        print("[LocationManager] 读取缓存areaCode: \(code ?? "nil")")
        return code
    }
    // 保存areaCode到本地缓存
    func saveAreaCodeToCache(_ code: String?) {
        guard let code = code, !code.isEmpty else { return }
        print("[LocationManager] 写入缓存areaCode: \(code)")
        UserDefaults.standard.set(code, forKey: areaCodeCacheKey)
    }

    // 检查定位权限
    func checkLocationPermission(completion: @escaping (Bool) -> Void) {
        let status = CLLocationManager.authorizationStatus()
        switch status {
        case .notDetermined:
            self.locationManager = CLLocationManager()
            self.locationManager?.delegate = self
            self.locationManager?.requestWhenInUseAuthorization()
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                let newStatus = CLLocationManager.authorizationStatus()
                completion(newStatus == .authorizedWhenInUse || newStatus == .authorizedAlways)
            }
        case .authorizedWhenInUse, .authorizedAlways:
            completion(true)
        default:
            completion(false)
        }
    }

    // 获取当前areaCode和详细地址（渐进式：先返回缓存，后返回最新）
    func getCurrentAreaCode(progress: ((String?) -> Void)? = nil, completion: @escaping (String?, String?) -> Void) {
        print("[LocationManager] getCurrentAreaCode 开始执行")

        // 1. 先回调本地缓存areaCode
        if let cached = getCachedAreaCode() {
            print("[LocationManager] 找到缓存的 areaCode: \(cached)")
            progress?(cached)
        } else {
            print("[LocationManager] 没有缓存的 areaCode")
        }

        self.completion = { [weak self] areaCode, address in
            print("[LocationManager] 位置回调执行 - areaCode: \(areaCode ?? "nil"), address: \(address ?? "nil")")
            if let areaCode = areaCode {
                self?.saveAreaCodeToCache(areaCode)
            }
            completion(areaCode, address)
        }

        // 检查定位权限
        let authStatus = CLLocationManager.authorizationStatus()
        print("[LocationManager] 当前定位权限状态: \(authStatus.rawValue) (\(self.authStatusDescription(authStatus)))")

        // 如果没有权限，直接返回nil
        switch authStatus {
        case .denied, .restricted:
            print("[LocationManager] 定位权限被拒绝或受限，直接返回nil")
            completion(nil, nil)
            return
        case .notDetermined:
            print("[LocationManager] 定位权限未确定，请求权限...")
            if locationManager == nil {
                locationManager = CLLocationManager()
                locationManager?.delegate = self
            }
            locationManager?.requestWhenInUseAuthorization()
            // 给一个短暂的延时等待权限响应
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                let newStatus = CLLocationManager.authorizationStatus()
                print("[LocationManager] 权限请求后状态: \(newStatus.rawValue) (\(self.authStatusDescription(newStatus)))")
                if newStatus == .authorizedWhenInUse || newStatus == .authorizedAlways {
                    self.requestLocationInternal()
                } else {
                    print("[LocationManager] 权限请求失败，返回nil")
                    completion(nil, nil)
                }
            }
            return
        case .authorizedWhenInUse, .authorizedAlways:
            print("[LocationManager] 有定位权限，开始请求位置")
            break
        @unknown default:
            print("[LocationManager] 未知权限状态，返回nil")
            completion(nil, nil)
            return
        }

        requestLocationInternal()
    }

    private func requestLocationInternal() {
        if locationManager == nil {
            locationManager = CLLocationManager()
            locationManager?.delegate = self
            print("[LocationManager] 创建了新的 locationManager")
        }

        print("[LocationManager] 开始请求位置...")
        locationManager?.requestLocation()
    }

    private func authStatusDescription(_ status: CLAuthorizationStatus) -> String {
        switch status {
        case .notDetermined: return "未确定"
        case .restricted: return "受限"
        case .denied: return "拒绝"
        case .authorizedAlways: return "始终允许"
        case .authorizedWhenInUse: return "使用时允许"
        @unknown default: return "未知"
        }
    }

    // CLLocationManagerDelegate
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        print("[LocationManager] didUpdateLocations 被调用，位置数量: \(locations.count)")
        guard let location = locations.first else {
            print("[LocationManager] 没有获取到位置信息，调用 completion(nil, nil)")
            completion?(nil, nil)
            return
        }
        print("[LocationManager] 获取到位置: \(location.coordinate.latitude), \(location.coordinate.longitude)")
        reverseGeocodeAndMatch(location: location)
    }

    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        print("[LocationManager] 定位失败: \(error.localizedDescription)")
        completion?(nil, nil)
    }

    func locationManager(_ manager: CLLocationManager, didChangeAuthorization status: CLAuthorizationStatus) {
        print("[LocationManager] 权限状态变化: \(status.rawValue) (\(authStatusDescription(status)))")

        switch status {
        case .authorizedWhenInUse, .authorizedAlways:
            print("[LocationManager] 获得定位权限，开始请求位置")
            requestLocationInternal()
        case .denied, .restricted:
            print("[LocationManager] 定位权限被拒绝，返回nil")
            completion?(nil, nil)
        case .notDetermined:
            print("[LocationManager] 权限状态仍未确定")
        @unknown default:
            print("[LocationManager] 未知权限状态")
            completion?(nil, nil)
        }
    }

    // 逆地理编码并匹配areaCode
    private func reverseGeocodeAndMatch(location: CLLocation) {
        print("[LocationManager] 开始逆地理编码")
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { [weak self] placemarks, error in
            if let error = error {
                print("[LocationManager] 逆地理编码失败: \(error.localizedDescription)")
                self?.completion?(nil, nil)
                return
            }

            guard let self = self, let placemark = placemarks?.first else {
                print("[LocationManager] 没有获取到地标信息")
                self?.completion?(nil, nil)
                return
            }

            let province = placemark.administrativeArea ?? ""
            let city = placemark.locality ?? placemark.subAdministrativeArea ?? ""
            let address = [province, city].joined(separator: " ")
            print("[LocationManager] 逆地理编码成功 - province: \(province), city: \(city), address: \(address)")

            self.fetchProvinceAndCityJsonIfNeeded {
                print("[LocationManager] 省市JSON数据准备完成，开始匹配areaCode")
                let areaCode = self.matchCityIdByProvinceAndCity(province: province, city: city)
                print("[LocationManager] areaCode匹配结果: \(areaCode ?? "nil")")
                self.completion?(areaCode, address)
            }
        }
    }

    // 省市json本地缓存加载
    private func fetchProvinceAndCityJsonIfNeeded(completion: @escaping () -> Void) {
        // 优先本地缓存
        if loadProvinceCityJsonFromLocal() {
            cityJsonLoaded = true
            completion()
            return
        }
        // 无本地则下载
        let group = DispatchGroup()
        group.enter()
        URLSession.shared.dataTask(with: URL(string: provinceJsonUrl)!) { data, _, _ in
            if let data = data {
                try? data.write(to: URL(fileURLWithPath: self.provinceJsonPath))
                if let arr = try? JSONSerialization.jsonObject(with: data) as? [[String: Any]] {
                    self.provinceList = arr
                }
            }
            group.leave()
        }.resume()
        group.enter()
        URLSession.shared.dataTask(with: URL(string: cityJsonUrl)!) { data, _, _ in
            if let data = data {
                try? data.write(to: URL(fileURLWithPath: self.cityJsonPath))
                if let arr = try? JSONSerialization.jsonObject(with: data) as? [[[String: Any]]] {
                    self.cityList = arr
                }
            }
            group.leave()
        }.resume()
        group.notify(queue: .main) {
            self.cityJsonLoaded = true
            completion()
        }
    }
    // 本地加载省市json
    private func loadProvinceCityJsonFromLocal() -> Bool {
        let fm = FileManager.default
        guard fm.fileExists(atPath: provinceJsonPath), fm.fileExists(atPath: cityJsonPath) else { return false }
        if let pdata = try? Data(contentsOf: URL(fileURLWithPath: provinceJsonPath)),
           let parr = try? JSONSerialization.jsonObject(with: pdata) as? [[String: Any]] {
            self.provinceList = parr
        } else { return false }
        if let cdata = try? Data(contentsOf: URL(fileURLWithPath: cityJsonPath)),
           let carr = try? JSONSerialization.jsonObject(with: cdata) as? [[[String: Any]]] {
            self.cityList = carr
        } else { return false }
        return true
    }

    // 匹配areaCode
    private func matchCityIdByProvinceAndCity(province: String, city: String) -> String? {
        guard let provinceList = provinceList, let cityList = cityList else { return nil }
        guard let provinceIndex = provinceList.firstIndex(where: { ($0["label"] as? String)?.contains(province) == true }) else { return nil }
        let cities = cityList[provinceIndex]
        if let cityObj = cities.first(where: { ($0["label"] as? String)?.contains(city) == true }) {
            return cityObj["value"] as? String
        }
        return nil
    }
} 
